{"level":"dev.info","ts":"[2025-08-01 09:50:30.423]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095030","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095030\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:30.435]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095030","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:GGon8EeVfJzOI9n7Xa9TYl4AGHgJOcavtBO6l4FvECq43bNbtBVxnI21I3gfDB7z+1QR4p8FnaSbd2/QprtlLWmiPOS23MzqMNq1sq3hdLhf2QnCxlmQSxJqrTd9BwNZ4bJGQyuOvaS6pWrIgteiLxQBlPVedBhHKllZp5dZ2GPUX4Erzn4lVFJS77nFDIhfhEWC8hic4IaHV7qpi4bj+6zEV3hBonGzWdfZHKPt0otdb17ztsQAFEFjlJ/x7/Q5KYD7RYXQPLylZ/JlAcVqLCO+O5Gayi8V6XLRcAQrFmJNsTsPoSOyRtpMbleju6KS+QfUKVVVuwNJXyVPANDNGw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:1Z9ihyjMWjKJjmO/aMr4EMYP+Urw1sc7yQtSoq/cWc0= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:rXJSoSBzq0MzWZFid86sew== service:fosun.sumpay.api.trade.private.agent.pay sign:eeO56RBsnA2QfqD4imaMt4rmfQ/+od4U8F6uRXWk9XZW19hbz/I8PA7t04X71cepduytN64kmu/j6iGpthjX1BbPXbC+nxxkMkc0w7EjjxRUICJPdaPJn6nJpUvZoNVNd9pfrbKcdQpuIG7BMF+mQQMwlQ0A++U+mzJ3DJlV0kiER78WaZ/+AFegLD8Bhg9BPtDOw2BsuWTytF/CwYSKkTPGSWI+WzryUrUFlPuAvccaoJlkKUH3JxWgq/5ZFT+H92HwwFm6Ok4Gsui0zT6+X0lZ7fHkloP3qlAc+7XtyI/6rl6JGxZp75N2E6vM7zVCGTvpW8PZkfBWqbeCEOGc+Q== sign_type:CERT terminal_type:API timestamp:20250801095030 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:30.653]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095030","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:30.653]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095030","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:31.024]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095030","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095031\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:31.028]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095030","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:avk2dZNVgQwZU7Itr+ybXeRVX97rvtFkgCimgKoRtIuCVeuXuuAy4zi0U1AkgG1E40HpuSdKvVSOUatNE9wt67alM6ww95a2hHfu3/nbAdT2bkuQLrrYIMAA1Y4H9vsxN6LNbTrmPqYnxX7NEZ522Er3/QJyrjbUSVVjqV1fS8GHn5ArAdlih9ODLjTsgVP2Ufz7NTV7aZO3yIG7RRmhBty7Ouxp125RqBoAgz7oI45ZlPHZGtD+kzIYNCLrLySiRDG7Vbdehcq8cFwxrgVa1S7vczvL8njGncthnE/8OrKoQLX6HbOmnLm0kWZAl5RAjTjx5ciTMpeie/qkqukw6A== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:O3SKpx8J3C7rulbmHUf7hKbXN/rJ43pqg8BDytZxnik= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:vu9bJsH/lil6o149J4vuKg== service:fosun.sumpay.api.trade.private.agent.pay sign:M12H5kNeUoA4Q7lVQ7DNJXcj0y86tCQR4mIofWwaql03OYymAyi7ZuyaYQdeHGNoB6gXwG5xB/mjBcW0CPDlNVkWH46YVbQ6xEWxOIfszBCpEnsadnLk0KU1Zk0wssX8dD1GSMjkN3JIBFXXdeTlodtOUq64KHM2hU2WfYbKdSeA7aoNwoz2KlqQ6eLwnwiXyAVFUbGehEjdxm3mp0Z84hrbwh+yOWlm1f+4wyd+QlzBzM4MPxx5/D+sJP9eWtvv9REIiu9c8KLbFsV6u+SZFDjMwv0fePk1UUEoZ9DgDvbBCheLgtUeXK4rb+SdGlJ2D4NiNhvu7UQrc+tBFqNuRQ== sign_type:CERT terminal_type:API timestamp:20250801095031 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:31.361]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095030","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:31.362]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095030","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:31.878]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095030","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095031\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:31.882]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095030","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:1jL1a1BcLiKBJfsj+R2uj5q3v5AybQDWEvLemj2qDrUI+KvRH1giXWhdB4cyx6ZlDCSM/zOCtxmtTVXVDD2siTBqMsg1P77GvVjHNaeNPhjh+YXKHkCYYI6G/Zm77q4t13NJd/NHZHYXUPDSTzDTZKfj/aj2BbTV1FeEv3ij+mmAme3lsfB0rropaVlegn8nVF6q7aWUXmiNr1F3rnxt6NRkKMr/NE3bGGPrGxGZYzCBQ/xKZ2WKFywsAFcuIXB3IQLiTUNXajS+N/iw7SPMIT6a/SPTkkUNr0/0x1axMv7p1g1gq4GrRPkG7NuHb/oZFyUmEDL9XYdUmzQm7dBcyA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:vf8BMAz5Lzeo+tNZJ5eyB6f8isCkKuqO8vem7MAW7O4= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:pmBuH1RFictrPp7peGcwvQ== service:fosun.sumpay.api.trade.private.agent.pay sign:TaAgs1TGuM/QTsp92LDyDPTPYInGfeIH5E3QmVYNdZulPRg++W8VLnnyzJximKE+IRs/gV7GfYbT4zabEunEBPzOR7ztag8dv6ZyCeejF2ZXV27we96mxc96aZmDmG5rB1KztcZt6lX9IjFW7bvIl6u4dJkMJUHCL8Gxko4TU7tZ8bOwH4Purir5Z02neHEifTgpb0GFLhlzA3CzU4gcRZYQvsgKZW+VUhAjEuHHwGdD6iUEMQ2tTL7ywjuY8Tk3RezAQ8WshVU3godh0zbGNGcKf6Hf9SpdUk/dbaiobu+MfJHEZrHyAodwb4shi0D0LRrTrTtS5MUwBpO/HHQvDQ== sign_type:CERT terminal_type:API timestamp:20250801095031 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:32.050]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095030","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:32.051]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095030","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:40.413]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095040","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095040\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:40.417]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095040","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:2C3mSZZvFirgHs//Zznbnlam+hVoWAl3zW8NLGDxupFGKDmJvVyaedH1QyFg2CxVbueWQ9CsCGfuyvbeR1Q87tEpXgBE/8CfdCNiWyBNFhd5APG3i7r1SxZmuosWsrnV/ib3xdWmfFX2aF+gQYNBBDnd5GPxOYX/Ur9opgLCQ6zDlAwME0LQ6I76hwsfKTHN6mD8WQqftasxnSkJBIdDP1/lwqE7qiixXtexFZrlzZcdipFaCCwgyJ/H4dqOFng1Uy+ql+SDGVS2laEJwN1vpKBTEVJZPyjRh1svvLJjUNyMSPKC66a7VsXm6juqU7Jl6NkmIim1Ym1c7SdUA7d3Tw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:MzzJMIguMrwp9kdaGdr4rens0rPFpa7HKkSzbAd8WEI= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:A9J2KKjnB+v7Rpfd37bz4w== service:fosun.sumpay.api.trade.private.agent.pay sign:fcAaPjOz/kcAdD+LrPyOk3RfK1zywh2gjci7ZrMLAoChcGYCH4mBi6gNYOSrmmjL5efG8gW2zQJVmS/oM0pkG+S9pj27Es23eFWGJSF3IN6qwyn9nnrW6pWSdjN4BGcDl5tcoY8l6x251a2T5rjFA3lyhS9gcMZ0gmwgrgyEYJWrwTsnYQpXja5PEw2Kxx76Q1UM97HIk+XU8kNlwZQmLKgaCVsp4hzFXwmhvnyu3Fts+JVSYKxFl68y+3dsWxwvKWMnFm0X3wbAfhYua75fm51Z76YqPjEEnnQtGmqPo8jHuK+/gpvBGz9U/hn9NU4odkWdf0xXKeRlY0OvU+sWxQ== sign_type:CERT terminal_type:API timestamp:20250801095040 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:40.611]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095040","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:40.611]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095040","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:40.848]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095040","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095040\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:40.852]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095040","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:HylR7KkRz1PVthaUkpNveyOnBvbbfaOEpt/1WTWCTQXCLhMbD6A6BJWoGvY+7BNbYS/0NQNG2VfTFoWh2Tr+7zdDv9aGkx3xjHDa6YT/9udXMRDwazQoDiNrO4GPPBbGGJP7CqIG+/Beq37H8U2o9XMw8fB/eqsEwOOZxOHZgwRyXr+rA0OMLBGsQ1r2xFXVAkq9K040weehWJOF7pg59hjKLU83ntM2+o1wcd7uOT4icuavhNhoDWAFcw+bxKDSxc1oil6cdpVSEfqdonF88qM8y6vxPzp1EhBrshCMB5ghZDEAabpsr0/GAWt4LfC9k38Q0bT41h1L2E1hEmLVKg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:H9Qr6+eO5lesHKTnQQHlVbcDLVUxN/bIhAN/RZAbETs= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:KsrG8BAkozZem8z1E3ohNQ== service:fosun.sumpay.api.trade.private.agent.pay sign:AzXSBxjXuL5O1bbluDFJwpfYofpQeZPWoq1NrK2H4PvSQ8Y5HsLPW8PeEU23SPJm7j6z1TOXI+VRHkXapUBhYAZmQI+kfYs12Y1cpEgOqK35qDxS/DFjlrHto9hqJJXCcU/SMSsaddoihoXm9MmkZR6rCU+PzD5FKFSSt8JF5P4WY1KCFXkF1/KBQRBfkD4eT1/G3u9Ciw/Acap03KZo3B/dfAFj2hTY5jirc47hpOKgN7lpK3Gu3H742HkshaLczraW+I59ibxBLKK5aB9GMc4wEgYb/d3dZ3/wc4vYAbnB8G5bwXgygKSNQiHOvbSVmyVeQpj3TYMuNzL8/QwJ6Q== sign_type:CERT terminal_type:API timestamp:20250801095040 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:41.066]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095040","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:41.066]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095040","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:41.324]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095040","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095041\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:41.329]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095040","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:ZViJ8Mpun9vbdk1Ye1dDIhVus2yj/XXPz82wJXOkRKnOSAT9rP1931QwNCnfxisEzKwK/4ZYob7E5hhxic/PbFMTgCASZllTwQ0MXJ19vxNGV9DLzeyZ7jUDHnp2FSCqVJeUaAVPFrOzj5oNVApWc/OM+RHUngOMF2foXmKUtOexJm5QIqdYEq0e7yEYIhtmyTtTejPbduRQ7XR6UjkYrIbwp2a7ivIY3kB5FibqfefwuDKOuEi6I5twcvfjduC/+/WG1YlcdBR2eTylAsyi1syG8dhcLb4xccTUP7cjktvDOxsqjWN3VEbNwfgD1Mhadvqh39dfTPn3qf8ZKvoZ4A== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:5CdpUkKtqTf31VMkLMzhOqH4vWz36qnxKpdhjyOAWyA= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:aGRuTD+qH5lrx7wbE9+93A== service:fosun.sumpay.api.trade.private.agent.pay sign:ROgcMNERLxInpb608P69BcQfIj4E81ZCI28xz560ytUYToB3fgIjo13Y6Q+t4RaJn1Nb/qgrQR15NL6N23FVrdm0R877jYUkLM6CF4ohxuSL8Mm/Be0zyKn/1bUGn4BKN/3DOXUcJKsEfHT9FvJFRCSwzk8AnlSeP6ht+txESyYMcYhWrLQxodzy0DInzncflvnXes3Dhgu1qcLWBGaCPkTDo/3HmI9ysN4/gbV7bRiGmS5+QGDle8E1YmQcthcnQD8nR5COVSo8CjdcxQR+lwCwRa2KJQ+zubTv8A+mlLrTE2i8k7V2pd8++OfPB8+qNZ8ci9LIeWeNmqipHEyJ4A== sign_type:CERT terminal_type:API timestamp:20250801095041 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:41.647]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095040","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:41.648]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095040","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:50.494]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095050","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095050\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:50.500]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095050","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:1Ykng05sEoKgkdIzythMj0WGZ29EOwcTSmDHSHZm7OlQmJuDrUhQp/h5GpBUWTP5CyN9nLESq3y8DYBqAYYZBYtb184GoyGOh0p70Tqk3049z5uS9IAfPDo2nOVLhsyOzBlMa9RaykPK9eGGrdwShKYb3zlAuXnhQ626rYWZR6jyXfy+KXdW73uYzMh8Gk4CpbKHMKFZRyHG/urREWoherMGNCc0T44aZzhirDe3FmDjwthCuXdA+z5JJukubSMRNmBQT+7ICP40rXP166T34wanJtx65QAAjUpj78V0UFiJr/TdeIRHWnCbuEtAfdUf6as+WHjBiSg5YbUHrOpI2A== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:avnF+CUgaywYbNqfZStcL0eG7tk1EHjueucanguLRmM= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:8s8TYrlWgHnV/Y5XiIXMlQ== service:fosun.sumpay.api.trade.private.agent.pay sign:XVnEYuaTExZzF2vSTIMtXtDy4sGQv210cXDy/pS7lMVoFNcApxLn6ew8HRc21Nl0mDhomNoDOpx2KQydJCih9bPznN1eDnMvQHqV8uzyCV0mwaaaKYfecxsSVoNfCAo/sh8rJ0pkwlBwixwkzjjs5aRt5B9nKvGz0gwUsGDJoUJN97qe8t6ulNTCBFXU6CQfyzdc+XK4qtwKWIK6l/JMJ47QC1/gVmWhuFW1eW3dLNA3uSGY/SnKfm+pYnIfueIWgMlOymiebo9+9ayveGT2ro76aZz5bCSuPMST9BMp7nrC+LFAFNcg1w+5pr4KDV8QRvX0azipwmPNC7bOhD1P5w== sign_type:CERT terminal_type:API timestamp:20250801095050 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:50.691]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095050","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:50.692]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095050","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:51.182]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095050","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095051\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:51.189]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095050","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:4kz5De7Q2jiQThkyRDLPS/cLSNkzV91e2VCwfv8s+iSbYaRxBT7NJl0h2GxnMDIjpXurUTmxVq+oe55MMNtP+Xi7tguxR3MTBvg+awSZxNlhBlbwKmcdA1ejKctfKsz1CYXOtRGlNGViKVpa1K0IJKxJ4EV1QxUAqW5Sfd7u+gnDbhymlmSyoFE00UyFPpqV1EyQPJgz51hHg0emGxAdY2n7fFXIb2z/DF7HY1jeCBDR88ycPKZwdrupm2kP/UOBv2yC3VFigz8ol281dKGU6WJFrSuHAjzUOOgFFtl0Ik3rFrCKomn37emcGRW97O1zciO4+8IouL9CIh0XA+2e3w== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:lo7NYuQOzv/Lnsu2jm0d23Gr48HbFDZ9V0eyFBnSYF8= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:8bqr2gyTnSDymMEVXb3qtQ== service:fosun.sumpay.api.trade.private.agent.pay sign:BIT3Jp/uvMkoXHkqsYS6BsVtDa/TRHVYxKIy16IYLUeSvHwUJNPZlIZq0ZwtEtLmrNSbkkkIKbpUGF798K0zB0Cwf7/qYw8/7ewSLhvPd7+JzcgpXMn6ADu2CFJSU4O/J0E/DIz38z1UIi4bma4EInBaJ41EfGEfH418doKbU/w2d/1n/+NIqmWabJOexubBC7aWdV3B7CHzSBBIB4iKahhRuOMQjOiCMZwOcGXz0X/751u4UPvmS9I08XoyOqO1VLqu0K9X8X9F/AUAqYKAD3JEGYxWB996vUUwBZ4JrWYc+RAT/jLKFzgf/pCs7PvEdxFhNmSf7VE/iOfiC+LZyQ== sign_type:CERT terminal_type:API timestamp:20250801095051 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:51.367]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095050","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:51.367]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095050","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:51.663]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095050","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095051\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:51.669]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095050","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:gFEjBQUKndFAyI/9I1eMd+96snPeIJjfskZY1kICPzNYco3QSZ3vxBM472G/r9ZJ4vZ44GECm95U735dDvSCvWuj+HqxTNfm9t3SLo1GJfsZG+nKppIJIUH5jkAjk2Qax/cBG8Do8kuWemLhoLKoGYs260Z7qWQrhij7nMeYbYx7/d4oyoIZDMl5+w9TDMmW8bsSh/5VpONwNv2opE9YeHOpHHOshfAp1ETMEgnD6mZ6QD5ed2C5M97QWyqOSZiY9ifuWyXOfvQ82xbT/RQAbNUL1RCADbQx9iQRGHVHInMnEYo4Xvtx2XbpSFF/5blMhRYOrZI0A+lDuWuJJBHzvg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:roveDa3dO4gOxnkCtYe9fJGGLV+60KCZb/UEFMKGhLM= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:3ziGWDMqRz57I+rewQeWZQ== service:fosun.sumpay.api.trade.private.agent.pay sign:P4iueH2mUVZI/lYtSdVu49OKQEGTxwPs+6Qv0OzYruglqRYzkhFH0/krjYqWWAC1/c9RROgXxeM2s3Fk1czBl3+KSnHnB0ONfPm65eU9TlQJLHeNDJvA3WVfpxTC+xRJzTOMMjS7u9RY1G8OJP4OZMmQ4lI/2zZbqNYF9eSVEK8TyZSCiolIsQtHXshYVnUflexv6v9zogpg+o43WufJh+7A1z4CuNfHrU5ls1j1/To/fVGu8PKnP2O0b8JCR/FelGD1ZRSD7+K8pin2JiSvLSqSFOzDuoqNYZkQHEG7pXXrXG/YaBHZWBajCW/tQJx+/7Pv7LFIqjbsHU4kokeyIA== sign_type:CERT terminal_type:API timestamp:20250801095051 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:50:51.858]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095050","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:50:51.858]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095050","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:00.288]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095100","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095100\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:00.293]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095100","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:zOZwsQCOST6nQP6uDkJm0ncBn8S/1Mo87e8+s/dsvTUzWgeFi+t7qaLbYjlPT9F7aTuTAJIol5Y30oBC5Wcj5cC1ogm0kW1iSqnXnoatlgfgu4SG5+SHWt3Ouv30xWzpyEx2E8tixjteWWfNc/EHTw/PSsnGTsNtj55PihuMKXZWGBI+2x7+AFSaCz0pzG3pjT2FqHQc50PxzGv7dyOobESm5yAJk5R08snBb+7vKTN6mHzNIeHTHYCqjz2zTshIePb6L+Rl6zaoRG4vp5QvMxgFaqVavx137+hEn/Rc9S6hx51WceVSiAjwfFsilvysC58YLYFrTlXiZ4v0AG6TwQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:yj4I1Herv/YrF+0ZmKa9NTYKiCnTCpMSL4roE0LbNgo= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:T3a8iplW2R7h1tcj+6BfpA== service:fosun.sumpay.api.trade.private.agent.pay sign:E89rH9IAUJ0x5DY9QVZXJkxjjGFETSI/+wWvIGyG/pjG263zVll1vIIh3/pKiz+55cIs96w1LHl0vUdVTckJ5JlNs6l//Ce92X1SnLrJ253LeR2x7wrwuMYDhn3i3o/t8yJ+4b5bKD911QRN6J10DrNaE4SdJyd7xSjry3i3HBRHsehpcxKxeFyF21Qmcw7qAfpsUA5PGnQHsjvUrhEKK8XHjMotdC0y0ZUvhc/8InNkdO7h6AXUjcwGedD4riTjMaRTIqzugLQ7v7wW335lDjqsMcSzl1nTJ1b2k3WAP3bwktVrPOVdjBqOWD9WVmi0SfrMPTSbTCf/JVlSpRVj3g== sign_type:CERT terminal_type:API timestamp:20250801095100 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:00.519]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095100","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:00.519]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095100","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:00.946]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095100","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095100\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:00.952]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095100","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:ecMi9z5BU4nlEeAhJ2SdMMRGt5woqTMiMvG8ApPmizDPbW35zyTlP/dRWPVYdcm2m8rD6pOOCl7+kaRn8KJ6QdRsQfjMxDTb3O0BFPxe+atYZnHrZZIZJgM3rGqPe36N7h1GvizoGThzoKSYB2/QlBhB2hoUSEhymd9E+F6RdSk4bpwHq07O92XmH4yry2WcxRETuZxKTsB38bNKqvXiRFBbSsT13+kiQwApJjAjoA+RXAN1XFVWPlHuSQdjqzJ5JSRKayTXAxR5WR4/rwbaWDPvnQXKBMxurizXfSx5GNC6mXeY4sEFx9NHCB3OPaOUKQesSrSJmjvhk2HeJ1Rb2g== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:t/8T/tTsQs7ss4lTRJ4P31dmaGhfxgrc3u2BoJE1bTg= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:mIAXKammxACrL/n45+jMlA== service:fosun.sumpay.api.trade.private.agent.pay sign:UI7LPStixH6qFwMuK/9CRNLHl14zQmIz1f9x7SS39Aw/WX2beZ5zDepQR0KiHoTA3QSouXZN20/ab/HwgDAUw6nDH0iT/gnuO1IoP2yc1JMuixrnTHtotvbTPKwBGPPjRJVwvHbROZCZdN5/XGOu0L/54LCue7E31xBZ/TQ7PlApDz7FOlKTcw0uQzqLNjmd2Lo2e251FZGgTvXJtihBlGvyWNtrP7gmsJprT2VPLH1DaMftLnkcRMXbsrBT4pT1haRYWUT06hXlYAXMbc/dML1vzIZSic9gAcjt6J2GvQ7luzn23hzmI2myenm4BKAJGErf9MjTu2iY0JRyqtVZkw== sign_type:CERT terminal_type:API timestamp:20250801095100 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:01.240]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095100","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:01.241]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095100","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:01.597]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095100","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095101\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:01.602]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095100","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:Tlu4q8nLkw3APQ2FHnOXZBCg6ZAEGi42ptKiUg2HTdZ834aLMW923Vx99zHThXZT5LgEGwRUvHI7JwbpzybMYz5xR0lmHMfmq4SeCRz3OeulRKfF5IOHutI0BnnuslAI3cwpgsrrzmcsMUqZUIezkXALwNpeFnFbVRC0Aw7IIN1pJSU+Q7M0nO8lKa0lWJBtgS6oOMiDk0d2P8VLP5x2mBqDnKzdFDXJY6OA69huIohJ5ZbWfS60E/8NATCgWMfcrlMaTDxjgPWDOsWw3P96/CROZoVxkEw0M+JELmBgjhSvoQqb9JFn6mzVjLVNYKDX1TJn6nobrWZDRgbq+CHrAw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:uM9nyQ0M+qUf6cfbSK2/by9TKQkEk4tm7DF0q5OeBWU= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:KES2yh5a6nrc+oNgy8nZqw== service:fosun.sumpay.api.trade.private.agent.pay sign:K4qyg78U1dUQlDeua2da5ZkVuI3/jTXGhwkw5YP0M4q27OajoGcqKepMfgwTHRMf0nVmo/2Baim4UpP4iGDGf7ib8kMMjSpNs7aT7VF9TNizrU2XbCPItZGujY+z6+SXsltaCMsnE8tu0V3LlEECx2hh0xRGx7k920oAuyVzA5cqaQlU7TyV/BIRyVyN+9YsgjXxSEI+bbKuZ4R5k0abu2n7cvHy/dq832qDWl3yuUCeerDY2RZfuHqR8KQvaAQtdSLLwrCcuG5DZCmu/Et9ADgLbWR+ywf5QtGllYmLvBlnWtX5eDj6ojHtZY19u+rEPoBZT9k0A7H3S7pYTRs8lQ== sign_type:CERT terminal_type:API timestamp:20250801095101 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:01.884]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095100","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:01.884]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095100","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:10.381]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095110","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095110\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:10.385]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095110","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:wwMNHIGRTlbwzfoSzLNdkHupnnIS9c+q/uX9ROQn/17gFQNUN9/noeZG6uKPssSOoQLJLii+86OdP/sIw/lzitLdoL428MF0GEMTQQ1oEy/ykjgzBZdhETmlF//+rsAkPZPEmLJf6BEyw5PUstWoGTzU7iyaVTCwVpNtj17QedegaUG0zpKi1AgBx9AsYt1p6BenZLgdHoe/iGq+IK0aYnEABzqg80HuTC4HNNgv2/3NlTNIyCQet6jdin11cTP6ZEJmFcTm1vPGnEzbLad6mcapX6Mex4PwCe8g+KmAHm1v1Q+O1cLpeEtluQ/3upitL1UBhovRxjiIDMmI5LsiEg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:i+X7M0H7+pgGOcdS4WM+tduovbFAbB8zB7YVEA6mtbQ= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:KmdNuWwBlGnVn12kEn1ycQ== service:fosun.sumpay.api.trade.private.agent.pay sign:RYFXpz+sNi9GPKPROXcQn2qzf64wFKIPnIdCxBdYp/QjPG1xcPkTTwMzy86KSXWxJnJxlaYte4tUYUUGiYNnCfIp34zdJFdq4vY88Q1FHtQ64m2TkCAttC07poXp+mdw/kMxhAGDK8c3l8VHxI/ZeH18QGTXZsixPcu4v9XXYsS4zxaz7t6bSYoTUc5BZ6WjoHvddBc3aaSCLmeCo51vjf3jQozZ9bSWWgk4oehHYBHAOZ0LsT8H5CymLMiJnqFs94/w05XvoDUR069T5DYz+hhyndczTRaX85MdMkQ1ss6h9MQk/h31YEwk6N6+pP9r5AWoPar/eTHLsLlMRk7LYQ== sign_type:CERT terminal_type:API timestamp:20250801095110 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:10.552]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095110","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:10.552]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095110","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:10.968]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095110","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095110\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:10.972]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095110","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:Mkke1RMAww4J9ps6hFnpkk+1qppZvze9Plo+9Z+vOssY177vklf5VPf0MP8X6+Tv+cTojOU2iJxAkQ98X3slPvAMwX3DODYRMjXeYKAYnw28lBFnTnz1NRWmzw2+88zKofqkx9LfZhM8sE5RKHm7zTmCEYSjzKKf16jwn+JkdsM6FVUnlUYSF3tA4fUdIp334cIJIv9OwWHLr76glaRUG8b1q4WRjJrcqwOgH7V6NuimHyAzAO3o6Ag986a80R3WR18jaJV7JjsdDpHQt8gkXYLoqGOzKTQWQl5Ou9ervbhI1SfEF+Fb0/MMrK/hHc1kbQjJAH3LQQkYtZYmSYZZ4A== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:KWnZ80vobl47ktrqEdvfyQ8PCOkOeVCctYpUFlUJ8lY= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:z0yLSOHwGygB1xyzXm+eKw== service:fosun.sumpay.api.trade.private.agent.pay sign:D9irifzjK0Fw7OeBGXus4PvX5Ui8Jz+CJnFwFwgyR3RO6mc8N9a8wXSnEPhjHCU+7TTFtNKPoZX/nbp7rVtS1YvSicp4UTA0teaN5FS9BOW5BdBVsc2iWgoLKyI8rvUo3GZx75fE5LWZzU+jTpeSs0YoLSsYchGuXJEN8TrTPvgHQwS43SEncsKnDBU6tcTIBufbBUJCt1S5dB4nHONnQI9AOvTnny2751YwtN7NWllyTuw2XGaoW3De8scFQX7WYzZyQrfRMkyiggETtTeC8msgGNyMh2rwaRUcRp08QhL0qlCnHyAmmypteUW4cW+dtx9ucRnkZQUZoW+kfJAUrA== sign_type:CERT terminal_type:API timestamp:20250801095110 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:11.115]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095110","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:11.115]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095110","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:11.644]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095110","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095111\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:11.651]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095110","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:QUclTbUoKl4fv7DHyaZhDSWCrynRGiuzlNTLco33pElBbcQb/Z300S96B6PxmxobEsJ2QMQiESjKNAWn2V1iunRFESkhQl97pjCCNGB4zdOMLVbMA5HgxgNBNm0EKnKQ9E9P+VnlJnD01Wtv32dktJq9n+emPEdPnGiRtJJmV2yIbAWcCEb84unGpGkJWn6Zcph4OIBVGPAbm0jh5UtijXz7pSkDA+bWyXW9KAe8LuzH5JY6ZdqCSntrTh4MNLCi73MScgAWARsmzv4g9Ip6kTmWs9k5dJeS2sZQOzhBOk6rW49SKmfkE+z1gOy7kfoowsv1FGGGcHUKAHfXvnTNWQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:Piv/8YcAyweTSe70bmaHwgAl9oMShj9tjuIGo7EYMWU= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:1pPG5xLVJgcko3DtNqqJYA== service:fosun.sumpay.api.trade.private.agent.pay sign:eTOAg/5KT7QyzGhk1bcaZ5+vXHKh474JKBuW3RQwBViFQivitepHmYpnbgJRWU7wisKk4tuQ0JJVZNeF7tQZJH5JrEaodk4n1VkhOzhC6pfindiGOGdk66qWlnAKfazLf9KbXm0Xof4Jtb59gxY0IJ/pHc5xFRUcf/Ps+sEv5yVII6g0CXGo0SK04diQewV6YmKFMbEiE/zqpOVYoWxAXWMnFfhL/zdD+CqfK/GgaDSfy1cBkizNgrOWByGvxzeejGgidlOToy8ElakJ0+kDvbgtHDCB9ne8n6qbSBBgt6im6p7HJFNVYln5cY9wexNMze6jQsKFH+MCFKb7JGAy9Q== sign_type:CERT terminal_type:API timestamp:20250801095111 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:11.880]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095110","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:11.880]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095110","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:20.683]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095120","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095120\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:20.687]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095120","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:aucZenr3XaWIfdzCfyTVdZ9BINLbDGnsGN7V+Fi4kZG3f1bd44ibs/CruCFBA1+u8RHZ6GEu2sFzPefcvC7d+lhJcFhfxI+uTMTNBSRXyEMr8lBKOZEvPwY7q4CHDxmzW5/6wCw6ovHGuqeBL6thhNjhPpFBSuPhywIlUpclw0/IUIhVLN4JZXmgH0XurZwHHaVm9m1Et+J+zazIKSVcMD08XzffKg+VIjb6lMmhQxxsrj2O1odREBw7nx0Y5/O2dFic0vtCM58gFsHTCv4V6YgX6iXLYiMJ6Sr2CXuK9uSdm7l2FKxtByQAOw55zdXcDTIA44bDzTmVG8FoZUm13Q== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:NWCuYiAeAeLljm4HuF+rVe2GllC93j6erqWQ5Sg0OU4= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:0nUUlutay/d1yQgAwAGenw== service:fosun.sumpay.api.trade.private.agent.pay sign:eK+XZ5rjgWnawdD7Yt7efcl4HU9BMe3I9wxSLkS37kBR9ZnCCr3BAGG71ZejQ92K9syMU4PWEbcHuBNM/EtJF84jQznjTnJFny/g7kRgtxvaSIJzjjcAs9IKt8q8doyBFSzxu2wAOQLScwlfsFQae9WweEot37f8biHq3aMPn6RTge6Do2pXGZkYcjQEJ9jOkE3dOpOYdpE4KIOmJZWDPUX6uNOkvWd7meW3BluPNk9qFDa6E2qfnSREbzj8Bs6KJvKb2p+TqU8dQ6BJAk/h3z53VlcgUzh6FMDY71AtVyishkxjWUZjafioZPvYsVaMIB4WFEzLCU4XWToiHyrBEw== sign_type:CERT terminal_type:API timestamp:20250801095120 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:20.931]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095120","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:20.932]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095120","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:21.558]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095120","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095121\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:21.562]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095120","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:BRUuvl20gGw6IAvZv4oQdBjPvD40/GXMaaf9QPP85CIkAQsMgBTUaTgEdNqI5RnRa88sKHYh5dFAv11XIRWfWCi93lvoSTnR0hf/gyt92BhqX+Dy5SD2JnKNdcxYmV0ipc8GySbGizddS3sldLUTDkveuUmPodA3t07JFXgsazXK+NJr2Ktqa+0aiT2YlmeBSJ099pUO/uddptWOmMDsWJRpKeO6cNso/iRJGs+bWyTPnWvvUKoXroIm5iCexrz+c/OrasvYSb364f2I71n+fmNHlQNOzc+uwe7rziVDSrWGLMR17GD4C+RzhOUFM4qkgR1vnRrqofAaigaippFlRA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:2s9S4cD8m5GQzq7rj2obryLdPVnFpuKeLQIn/+vgO1s= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:QhXKDQzTXrjy4oR5hyYmvQ== service:fosun.sumpay.api.trade.private.agent.pay sign:DoP5/yV2vTx6ktL77xgEE3tLqPbYos84zONbCat6OvpsIeNzxe2ps9BFCOjKUlDOnow6rkq+ZLUo74M+lF1b2NFwfj0rs05lMXGNPe3Ey8b7GHAOSasL1/mp1Q4G1ldRQPsOzjg1qzfEf/q6AlMdToJvERrkvR9aVit0Cc7m7+yewFrqsfq/Yv/2hNc9IS24sW/32/D9xwVCMRJNV7UJBDSOzfUxNlM7rqOZYisw2OZoD6cPpHflizxjGkkkMayaUv+PG1gILbrDvvlatFICT4PYiGJ9rtR/1uLuK1HlsAckySEegHkjIieo41LRw8mJKyOI2QTm2NFXg3hvIvHheQ== sign_type:CERT terminal_type:API timestamp:20250801095121 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:21.722]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095120","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:21.722]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095120","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:22.352]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095120","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095122\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:22.358]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095120","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:RwTmjAfbvLuIIkQnKPY+wlQM7S4q27CxYj7uX1hEDRzx7YM28+cjg2NTOXj0aRS/re7VI7kjI3kkuCvWIxIx8ZFn9p2GQcMcPASl59VQCIb1gK3Vcg1YybDLLSrZ2vN5L8rop7dhGjRzNaU6rYO/RemgRNZyOOqscVY1EX3IWPiv1nzt6+q5FLyCe1aT2oRks+t+R3wwPftur8908Freaakcopqn9VFRF0hB/jQ657JWET60WfNxdIM9JYR9vnzsFXSA28cxznC9MHO6xG4BxX0PL0qSqxPnGJw3uGM3pPx4I24rYQnE3mV2s/qBOxAOvIn2oGMwAsKFT2fvhBQZCg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:WLyn++lGSziBCX3AGMylHaAm5mBB+nV5IIyZ4KVoZqU= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:Kz3/aupXIQlvVqQPGBa/lA== service:fosun.sumpay.api.trade.private.agent.pay sign:azcCGDEtw8pewZQ6+usKYJlwKMN3uiUUKOnMFwDgPUfnv1x+Gjk/KterbTmfi3BWbQ7s+hFQUlvy4FO+QwFWemFGXAmbfXUBt/GZWYknZTocHM1JY3y9QyfgIaEIh7Q0FBpRzLCvPec7dUySnOifaCX+vuaTaoAIRVUAOdzV8eFYbBrJn9KMwJj93UqXAPiGLMqqSBB7mn6Qkn7KfZn08dW2gVZ3Gy7HW1fGNxetAzQ+1Yb+s/IE6RZb3scb1nQ+Zioxor/fJSoLhqmRjLbpBXZeD7LDhgq0ekBtr0BeKhpmw26YxixnukmKKq70S3lApmcOp+v3HBHstJE/eFZU9Q== sign_type:CERT terminal_type:API timestamp:20250801095122 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:22.547]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095120","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:22.547]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095120","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:30.548]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095130","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095130\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:30.560]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095130","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:Prs/bVioD//5Q+DedPlGrkbCAkVTBP6IH00qhGwm7Bn86mGHzv36TncfTDstui4bG1WmhQgIInIDk1pnQnFcMUCQhzAukqzHzGYhlozIrZz1SG112JIPVXV0+qHxkEIcW1exuO2ijbu1wJ790VjyHgQ1E/m1aYin1Pv3RdlR5n98dWTkebumQx4so77wjD9Zswu+Sy8pzrmR++uXZsUCJKhYFvrbbhujtFHHTVJ3GakTf44cspA76DEJxSSjTqPzbUfcZl2/gJtZRmW0m0Y4at1kLNY84ik3v5dBgYqwf6Lb7TbOzKXua/vDbnMwB+vEBjZwVSNMblFvq5/bhnWAVw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:mrkAV1rIrybPqJoaycKCjgxm66/FvR2Wu8hpk3Gwfv0= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:lOoN0UFvt2ZOOnUMSZ5T5w== service:fosun.sumpay.api.trade.private.agent.pay sign:jA4+lP6e5hXwN4D49+qkMRRKiT9JWQwTLjqd/z3N12t0FuIoKM7m5yyZRFAq8+y/Um6ZefmZz1tW3C5D20A+xjz25tJBwuct1F7ieQEyjUsjo2IxUzVLHGjkNx96iiq+/Skj5//IYOGTkj6GJspbY3GPMgf+70w9qVPIKD7MKW8z/erMWD1MNgMTTzblNj9tMlixqfncbVGkEHw7i98ir3FJLG0CO7S3JBX7TMEZnWcpEv+c6pvQR3hwPg0ASi8y7zFvnSSZx5cu12zADUL8G1wvoiHx5OFNLVS8TNerJy+zBh4shLwJ0IA3vxVIs5AibCOxrsS2TDBygh+UVrUsNg== sign_type:CERT terminal_type:API timestamp:20250801095130 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:30.819]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095130","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:30.819]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095130","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:31.338]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095130","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095131\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:31.343]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095130","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:LucEMG20jW2ohzhE0mdIfCtdLkbrC08JaeNO2J+NsJGlR7M9ufL7vUYjj/pTy4cl/vdV979j5P0GS0F7faIlWc3XJrK22kT9bashJCgsQlkl4wURHTzjGxqOtogqXt/agQ6UBfp50G1X2Pc1JrwmUBSVmGqaTIMXSuT46mCxQdZ2oA4ZYcWSZh7IR0KkF78RaPbuzgZqQnULutF8hK7zderwrX2eArQtIjn6X/FeWlr4aksuQa+YViQczOAtdth1QZ763AJopfBIdXPzfHTCmZYhBRnW6RkjOMbLXK9r0IUr4q5/o2JLPveb/g/4NHBpsGbmQJsqB6JlSmLaV48ruA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:gVSHGW/USx3/6bVSAPHiet15c01MxUYI+AhLf33qsxY= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:Y5ONPUQWJ628yenVaIdFYA== service:fosun.sumpay.api.trade.private.agent.pay sign:Y+D3qRaEKUscorI2vBy2ggS4/utH7A2gT9vuKglivx+bDZxLoyGIiFRn6mK1ocTGkTuPBRQIVhZCdheGcLsocw6Zff2ZVGZqQ7wXo/6RAq9TTzGwMeTmvLB/1jCu/VDd/pBPJJcx2WK0hbyNngpnLN2NblUNNm7+3NNhF+ardtD7p0CTnAhQf5hE35BxLppQz/cqhLM6SHASTF3vlFvl/P3CHWuYFlGpmXmynfcPSrMZkatQZh2tpdpV4Qa4qDyQtJPOJ081qP4tB/FY20c8Z8h3xHScqq6WC3PRvimvSISuBUZDw+xy6dq2VR8LHkvXqqd37AZgedxoQqg5xdU4Gw== sign_type:CERT terminal_type:API timestamp:20250801095131 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:31.534]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095130","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:31.534]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095130","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:31.804]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095130","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095131\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:31.808]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095130","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:Om1tCLTG5QNNhxlw9Tb5KoKCQcXqN+B6y5DJLytVMFfajPhAGME8cJwMEaVyFpwfUer/bxS4J/J4oKFPmrUIYaZ/7DyPRCR5RjCn1KrDgyW4v4TL+kbddo0K0sxKduzYJ9xEJBiRswG1tUi77iORs6Xl6hx47+Z5OZsFhXv94vkyFnI7+zcS/3Ros4kSdRxThyULmOl8xi9siYYXtMueO4vWnEvkXdQLe1OYwcdrD+5YxOiw64go/u65NpXplMRqbglUyG2dqTyxk+ztX48zhOYmJzTk6OnFfBqMAXICi2GgLa+C8lyZNCky20nFu9ns69ecRgw78L8lGdsWNH8i3g== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:oZL3+bQkEY09v518A4JpH/1/N5l++VSTA8GBf+eo3As= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:ARCww+RUJHYqJI2438bN8g== service:fosun.sumpay.api.trade.private.agent.pay sign:Ychx2XoOIgf+H4YfdXQER14CCzIc4KD7TaUgQ9DpEJsig9wfSxoq101GZ8FCKfxm3tMonrdZzLUrurcteDfsjewzz3Ujze3sSsL/OQ4dS70Arw0HtEb6yb9e8mZv4lmTlOBXiMCIj9Ve2UN54WKJ/cBtwKqGui0yZPq2Lit9G0FuuHfb9qcYFJz+Xk3uqN3Iptlz2uLulYNsgWhIVK8L+vs4mQdFgSGpsF1GJDQlVZod9paBV3p+Vt+Z/SeJaowfWXj50mBOudR0huEgObeEHfxpCtIo0KsqG1YmNbKrrd8UwwV3Bm3VniRzl6+wrYUu5uURmLaJdnh/ho+Cwj+WZw== sign_type:CERT terminal_type:API timestamp:20250801095131 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:31.982]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095130","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:31.982]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095130","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:40.425]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095140","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095140\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:40.431]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095140","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:xrmOhIXqS7+xRkqIpEw/i/J2soPW64EV9Jf1EEK4QWLoXmlqTb3QH6bFieRIEiTqUdVt7awi2LRM4Bhp7ziVdOtASM0OMbld2ICqdudcPyavUI+e69LeQkeh8ju1Ny0lFf6TEhb78trhBNkkddol8UmaoGIfu40kdUc1n0bS+MYmwN2/0CdrHuOesqxXm4/91+T3/7vl9DCWBCmgSufClF8UghZAKnJpWBzEJwRvCrLYkiZqyjMyQnIk3KRZkp08XJqXl1PXVZTu2xFKKmff3s9PPAldcxQ3rDTGnQNF5DeIlFJIbvMmQgrgxwJfTBHiqtFkhJaJJnFa/9pLnKhcNA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:qjHEvBWUA9mN2St5cDIQMNTfO0jFjaLCnnwR70ZmLNw= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:CKMtyO0b3B4ZpmU9Hf2tQg== service:fosun.sumpay.api.trade.private.agent.pay sign:JzBr9/085w32iG84p9drZd8rcMUyJSznBSCumbS6rigriWmXjWm9GqgXQNJPxTl62WIs2CJXUuX/0QtQtxPjLjzWqbMXrFmcfs0cso803vC47X0BM/ygY2NogVbIGVAnM3vNrG3B7FnvUFx1piLj8tfROg6JojZdC2I9GlrJuy+GwNquv0fycj0gPBpNMObx0UWL6LCyzwvvT5mT7zgC7EYejIDgyo9ZB84YXbE2k+YqjMS5RYnPbmpLuUqr36nifRxwLxSLna7PsZQZ7mhh0QJN5gzPEw1knoOwahwloYKOFDrfFNEW9ETtw491INZi8usjhDjC6vkvE1h3t4xkqA== sign_type:CERT terminal_type:API timestamp:20250801095140 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:40.822]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095140","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:40.822]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095140","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:41.290]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095140","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095141\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:41.294]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095140","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:FPS0FmzmgV+jH+UTjRLOpqPxNezmZEp1+7EmyGSxLkkzUqJG55awYMgGyEEcodplZGY9Hrtoy+26oKHHYSXhp4ohKdw1fHpJUX8sQZC+m7k/AgYT9ayS1e2b5kUnlItCMc4lLVOTheywtc8fLZz0cqCdUyf4hzJNZu4fgQ3Ogp/dNpNumpxA25eHtWWQG0HwkP9l967i+LylC7wVdl8qBu1WTg1Ov/EfL6ZZanot2RQUN6Nep2McfQJ9iPv3nIRW3Zh1d/Z8gf7ZAl66p4zyiGPGhgxv+bDkMwlC06zavnKBQSYm9maLOPHTOABEHfoA+BMQUoUO2zeE/TlNZ9CgdQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:W6qUd8hS25GI5ac32PB6o9zgbkZJypSMjuN8LcUaUbg= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:bJIzMOoLOI0NCKOqkyRhKw== service:fosun.sumpay.api.trade.private.agent.pay sign:X1LvoRIJ41JF/rLK0PjEpAcY2VYbHB4YsFMOLufRhyu0UlODTHjqwLXvGlSa5GW/h/ZWWLlT8ZhBmxk9JEO4D+Za8e6JYIchIH/y1fciqi4AdObt1aNsPhtXUSuXp08Von+bDlL/ZF++drYdDeVMBHUcydJk0HSCnESTOe/2BBTIS4uJCui2VniwC5H6ZjP7OaEcjQHf0jarAw1idA89UP2GLEdAGiHC3UZHr17k851yTlvB6tdnTSwmMFQnZDHHoRJg1XVsz06Se9FFji6DRhavCrMI13TZSInYp9AtBE2+DQbNJcTGOir5M1d+i6gcMBkPa1MF18Yo0AX7FYUonQ== sign_type:CERT terminal_type:API timestamp:20250801095141 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:41.685]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095140","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:41.685]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095140","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:42.649]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095140","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095142\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:42.653]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095140","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:6MMJjLRX85R7wFKUcl/tWDQsj89Lnrpf2nrDbxP5dt+r7DkSkIGhBgIrvNe9Z20gk4g6GJ2IXnoyIYV6xYN1/5yK7dmyc1UaQInd1fJqxDPTj2QXlMqpbGWs/yGqOY3GxDSQ/uy5tWi5sRkWb3x3VIkPHKXazLhblQypRCV5Zx2YH046cRHqv7udbicA10RpgonOyDReoxlhx3nvB9Jgzmn4uTkpEFmGHAsxI8T28xUj+SB6+FryZJre6iN7ZPSqDUB407YW4BxMeyIzv0XbeUIFNr3SDfk7rGZRBedIoi+BM/WM//DnIKIHYMc3a46fVd6BT61tW/ubgmNoJy5PNQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:Be8OWxLxycY7uruxICmp/0sBKWo1JZZeihhKB1yR5NU= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:vivfBsvFDE6EhNykKBaRgA== service:fosun.sumpay.api.trade.private.agent.pay sign:K7/zG0c/r94Z8LwQowvkYzAde1ZJkcAL2arpWVB9xsyze26kR8ta7B6y+4JfFtI1NsGtyapv48/2HD9x9825GtJi+oIhkLMioJxmeVz/c9y+eaZgWTa1l89MTZeUGUwJl++5SQYyX/1lI1Hb2MnO5bzBESQqQWpwLyPtoxfYpV1G3teFzuJshpZzVE+e5uaEBkadHm1IfXQiUoPiCRje6MJ8z+aPOaE3GnRzn6OSH9sqBabzHawVmKOgloLHTiHD8cLNt3SJEKvUpIgV2D/vGv8p2klnszn5Fk8mtDcBIlkSwMP1Fsf0usncAEVAThNvXNjmWGCYQu3CzduC0DBSNg== sign_type:CERT terminal_type:API timestamp:20250801095142 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:42.863]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095140","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:42.863]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095140","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:50.508]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095150","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095150\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:50.517]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095150","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:Em41qhZBugyqn2xTbsEgCMHLqR655eSK2mmqWSAR2F6Fiozv8E3odSsoZ/bX+u/Ze49FcUKyY3FkvjQ9jdbC3sEzwQYdNytOr7wxRe/Rw6Y5U6Qlt0OWSICh2VY1XuQqT0Hu4ebsFoo22yYnO2rT4aJ3uhKqNJe1TjeMG+Owll41CLrx0sNT5uPDO3Th3AVBazSBv7tXvSnjWNL2uTocPadYWp1oDlQQkNSY4HJfbqRYPRh+NUfQM3ZQuKFtwV3BlyrrqCa+wMliEOZEs7nHc/yg0H6gNIJXunk0KD54VHycfX8fqFdicBj18G00pTh6l+cpcd9FcR2o2YMDry2IyA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:MWtG+ZAiQUKgjsBbzascRhEpVeI9X3xuBRfec6xWIAg= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:YMgzJaeV6Z+o7JVMSmn9Rw== service:fosun.sumpay.api.trade.private.agent.pay sign:VGrQAgiAyu8KKkWpQPcowrDni12Nwrss273yhUHCkZ9Ws5DzX0XItsZJlX2g6N/IlBqW+DbrBKwsAk2wQszlTUyHHKVKAeYwdXPh+OFlReq121XC4RarNAZ56ecRN3mDiJiK6BiqqIS7Y9kp7M9xgjlsjOJDlAvy7qadcQTNe6VDKYLC46mipTriLJtB2I0+svshjkLs7eOjnZWssQnfBpRrFlUJOYE6iKQTDOwB6PigkP0eW+D8NJCH0RgSvYs4mJMWDDrUw5r45yToBefjd0y5FfrsOLKiOJYxvZEY9qWDFGZ1YPsU/0RGdCQQH+wp5ThflCUXUmzRkmXHfXURTg== sign_type:CERT terminal_type:API timestamp:20250801095150 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:50.718]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095150","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:50.718]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095150","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:52.490]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095150","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095152\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:52.502]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095150","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:h+VMlTx0N/Gk8qzyn26btkwS+UzKAp7oejNZCYBHQb2nDwdXLwpHZD2bOqHEzY2eMl5OQxjkZArZcWpYMJLTZpDwiKJOMRVuIBot/p3YtA5Mn99wV3cJIhhuVK2V+NXh0wxvtmRZ5yrLuLxkeRh3vUueVKUlzb8c8FU5vFaa3tbYh5YhfTOdTu1Zmuof5dE4y7HDwH3F/xdgcFNSpToaAmqEHn4MkN16gkuqei11rUHfG+ve1mCD5Obw9ODFvfAnxvsR+7fuqg9Magwzimt2j/g5cU/YBPcmSoa3m9CQHFv/aOnjN/ihQbkRYEeZThEQpVlrVAd5AEGIbPlNN6mPJA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:v1cq57fOCH+LPieo9ssV19kx0wg8gJN1JJcVV8I6Z/c= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:p2/gKk0DQWtz+FN4LWmZtQ== service:fosun.sumpay.api.trade.private.agent.pay sign:kz31LS1D9udBxvDlZdIsop4oPYwlJl/qlhqz4YctRr40QKl8yhmkkbJhDmOdVYLPQ45M1eknLo0Z7FgUnC/YFxXBnJirH2unEVdBGggzTsXJEKDABeiFRC8Ieijo/CE1CS6m3jcoWl3jZ/L6Paod3Bi3CYZG4uImDGT/b03/S363npR4redYk9RBsNIbP1guPZccaiYCzKcdEaDj9Y3Az/AScTEHH/tmkAyt+GaNnktsqiUZP3AcWDYzAXcRZCSoSInqgu9Y+CNPPBKKImcs2PxnmL6vFpdNxKh/zgG8+qhe11vnE8VlYuccosEGF8tVufMg08A6qsGyefhz0iww3Q== sign_type:CERT terminal_type:API timestamp:20250801095152 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:52.856]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095150","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:52.856]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095150","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:53.507]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095150","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095153\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:53.511]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095150","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:dZJ87MffvKciDN2rGmCE8TPb5BET3XuGtJP6Fz6eHtGfPQuQdY9NyekpMnM5aB+4skCYEY6EPEXXLqDfa6sKV9zBNTHT8tOAESkjsAD5/JL4sepnJL8H4dzXGldONcuxjtNflz3guIi+1FyT0Od5sHHFNQNtGx1mTcvuFDSUF6UZeiuTH0Wj6l+uuJCR0RTbyQ5DnNqjCiYuCil6LNpkh03oDeM8EzPLssXKtHMP4w0C33rr/5nLSq+so2M0im0Z6Zs5Uf+RsZdBc+AbfF8FJIr0p5y5W4pc1SrxKX4JVeQdDrbLAGAXlx1A7rVKnzDuWrLsCiVe/PIW8lMEDdNmlg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:vkASEVuRhlUC1eFfAFYZgmjyJyJTyRzyTVx2Pht2flw= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:QRBddafS++ICBKF1f9aNGQ== service:fosun.sumpay.api.trade.private.agent.pay sign:csuT70UgCq/5JZWunQN9mIdgX6zijhZbn30FT9sb/vJFNNg5cbokDd/N631V9r5gbZZLotYNesbb5TCHxP9QeDegKLyIHxweWEbp1bEd9FWJEm5RjqvKoeTy3DEL3s+4riKfde5j8/30hcIOM0uFygP3lkKmB75J5TJb82oZqLn9aOdG93Ov0fixR/fNh095RRyMdlU/vRGrxJNyu/e6bVQTT8BznraMlqRseqyABSuEjSe0LjTGbdzNQo4JmDi9bE0rEd2XmTyuL495loeff7wsS0c0edBcHgVDwP4NuLPGeLmSFuZm5nuk0VtoOBWylVyejcuFuMo60tLuyrqLwg== sign_type:CERT terminal_type:API timestamp:20250801095153 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:51:53.872]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095150","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:51:53.872]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095150","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:00.498]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095200","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095200\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:00.505]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095200","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:LKefpxLsEdXX3Fdv7WSSqqTHbXxB1N7+2tBHYrAqmEm6TN8+wD9fTbJY0jU7s8ICe4mqKdjRQPE6Z1+6nXSd3BjY3sxv6O+rDIugE8QpMnlmqZ/Tsio2lIddbx0fGOEYAOKt5rZJOKJjbmd8GEbIkGPpDhPVQfQoYiRzTSuf7gCOFaVKBLK6k7u1bNE86KanFLaqiWspaCQjSNUWudNgS6pK6RU+cxkw+z9ALZ+GHyE95SwYIAuAydyl4UnWTMO7k7Dkmx4p2ol/ljRED0NMd6pN3a/OPMJHm1GW2f+wABT0wOiluGxEiProm6JDCtK1gA575on66Gf85aUyYYPzoA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:FUElIbKC59+DIE/RwnbDfnO2F5jdNR68T6mMQaklUKY= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:Irx2luLWQjtg36U2Wyn5Xw== service:fosun.sumpay.api.trade.private.agent.pay sign:SXijujDa6UpSE1tE+5YgwNeoOC16cTuAGP1MvLw2CSfyLdq6HAXOnZUEsbN+DhFfBoyP1nUP/7qHZk57wwACZ62Pupdpco2BwiOBK+m/dTSl12lUcpZOHc5px9wqTPsI1SESKtwMDwS1k8PgCIGi22PhglebVhLQ8biwyfiAUKxyzBPDGNq9EqNhFMNNMZ3ONMZMDWlWTOXByTiWdIPrREo/23bJ8/iHVphrXmbBJILOzA/X0ppPLKdaw0dd+DN8v0kk8UvEFNxfC8OhvqvsipHrno0DLEtgFxI9dwHIExpk+xDDZYHUz+dZzPwTH+a2qWMIcvVPR6V6r3Afh0FeRw== sign_type:CERT terminal_type:API timestamp:20250801095200 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:00.901]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095200","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:00.901]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095200","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:01.675]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095200","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095201\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:01.679]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095200","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:MU/koeahGBXVH3UtOVN2ADcWJ3ByAG6Oc9TGCWeC9GD2knyn/pWrtuLa+uAkwBjA39t9YMQg7PtIGlOMCRz+6KFJwwleECupmmqygl0Ze1I0NvyWs2DrdtbX01aum3NDx7cg7RADAgQU7RPexzPQ+pjVpYHKST1hL2hu9pkp8SCE53bGSi4kqnz/Wdfvrh2KNSFHfMbxuOgCjzGTIeDHcVmoEeNm/Gw90UQPaVpmF9hcrSFtIDLy6LAlVdXeOspBP90xBP6jYcIv5z+mW9YR5lJ9gqVLYGs+tl6tKaIviBKOGl6HqhUDegTQn4bxapRuCHlf2K6YsdGD9BP6U3lM1Q== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:KyceIufBU5eFxF3jFTQQZPw1dJo6zDebh1fHEBPIvCA= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:KZsimunW89QtmW6XCmIWdw== service:fosun.sumpay.api.trade.private.agent.pay sign:LqLPdDSt5GJoNP34sRW9tAkZvQh8qjS+dEI1u3jJYsVW/87ptrgvUi6e5hd4ABaJa1mb2Jg+3KOYjFtnkPh8ITM8kvInVkr0PJ0k7jaFeo2hC74ZiHuLq6UkI8j40EvbWVPjAp9KoljRh/iSBkGY63hm186J0xPP6LxIxO0GShAcF+uEWTJIYKj37e+CbLKacHehmYl2r6XLr6NipcfBlD+sw37iOl6k0CKfxxPmaJicB9iOzhfsLWPtWkLT5XZ0vI0R8piMSb+WDQ4MeX/MuLwwzO7k7u+9shf7rGZaw4p1LqC/LCCOyQ5Ta2EfulkUmePaYXzRN14TIjOppmqqRA== sign_type:CERT terminal_type:API timestamp:20250801095201 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:01.858]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095200","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:01.858]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095200","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:02.718]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095200","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095202\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:02.726]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095200","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:6vOy3JvHpMHwfVwtx3vv7Wywi8x26yuu46s0IaDtj4zkqqCB9VFyt42cU3/HIvNH/OGwr+9mWgglLPvNXMkmQFRxCDrJQuxU/WYRWaSRWmNdNvcqaBKk2KfNsgM69eNFUiByD7ocyPpCAvZiVSkU5cYeaOjK1cH/jP+KOF+wXwGgSdT12W730Gl30FEaPtXG1V4vV3KAVFpCrES0FT+8TsryqJYQByOcmcOf8mZYUN0E7a6GNBz9frwpYMpo47N0jCD8CovPkyqf0ueX6szKR8Av1Gr5I9Ys615wfjcC25odg8cHiot6UaFvyBEhdGImtUg4ZTVNGvl9J1hgB7Ay8Q== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:pCkIBrvEpdEI/ol+4bI2L8C7vRxdkBhbf4bOJjfcP+0= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:PzK4RMRugOvGERom2iyZMA== service:fosun.sumpay.api.trade.private.agent.pay sign:kdF4QMHmd0wBcKvbRVFIsIHnxijKobuJa5LqQloygLix4d/UQ1yvUVGErtD3ElpAXNCRw7HzqWsCY3f7YSeF9qQcNJnccBFgrJA9auaZGwLarzCazLJl/ygmA2OViaFMh+wt/Hh5FTnOTxgRqySqMW+lzAUEWHeLzEMFywdaFwK2hY3YNAXK58bgDF0DvCtGdZE3sPoY0G3haNbPHKbjHULhEuRpuqBh20PE/zKMkOvtCzB40rMtkRo5sQv/Cxzzg5fb2WyV7LdoZAppMJUYsVlqYAXodSTzy1dZ0acPDouue47c1+HM2uLPUGNAaph+csq2lwA12LtWE3He+dtX7g== sign_type:CERT terminal_type:API timestamp:20250801095202 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:03.006]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095200","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:03.006]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095200","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:11.109]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095210","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095211\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:11.118]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095210","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:x/V5EE5EgVS11qbJDgnNmA9A6OQfaCQ3bpUVtAHXJ6+I60QEBlej6JM9STKUAZJ6TdRtGTxC/vrVVoilH0hqQYRpkC2sH2O1M8zVGgm/3VyPbSfnArd2bNFvIOegzcUIx1+gtbfi1LemBKA0MUFgmgpcGBgMUJrxDewoxSavvhYA3bGSzLzwzonS27QYSTg099Ki3prSZ8hxFfcnV/p0TemahPekbaoakOfnq4SnFM9ht/bV1TWmL/409Fmsk3pzir7XyxoSoMGskHuwcowfdPaRf1RTg4MWN+1K4fZctfBOS0yAPjfMugcziX70E558Y/y6qiUm+Uu1gkOl8BupAg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:ptfN2d9hVnf+R+G6JzapFtTOCm4dUogGtkXfwoW441M= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:wGGbeW5QE8siXO0RHdTlSw== service:fosun.sumpay.api.trade.private.agent.pay sign:CHOkwETtCAE1Ms3vv3zMbjC06ezLBCBFGPTd+i6rJoLpk4Bdd305+OprkRaHn28zRHVqmjdlwfLV+9Ob6Xsg1QEZ3L7uCa72WJeSQZOLdJ/mBYp+udhrYvaGeRlXC4eE1ixdahbjDAFMS9UznOMlBdxWdmGgmWOPoy2asI+davCFf5D27COjVFVXP7qKfZk1BXBl+XGwccpmoCOeUNeMCJswToBAHpnGIB7kF/XSvBay3LHkfOsh/5yEmv8m6ZB1blL0xoI8sJekIEv1ynJBpVMzC80WBobhe1QSjHxFreSnuczN3Jzggz1K90NXnd3498xO0ZEB3290P3Gruy2yAg== sign_type:CERT terminal_type:API timestamp:20250801095211 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:11.372]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095210","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:11.372]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095210","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:12.542]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095210","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095212\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:12.554]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095210","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:ixApNVRx1UqqJyWXw8gOql0VgK1X5XeJ59M5/GwnRz94jI3+NgaIgSHmfZo7C+gcLnRtoepEJgiUJdgf6kA1BqKyy6dKdi3LWqdNFbNXrvdXIb7cJXq2v/wBeNeZVN2Cc0vgUHgTJ2YNl3PCMGkfWHJa76HOTZbxBIzQx/SDXXcXSN4ucYilFaTtEll4AL16xgAb19PB0mSXctq/4Xl206Hwc7+JM5jORdNAxae4vpo8KghwNjzMpkl3T/a1d9HVwpnGauE1BwLxt94/zu9Krv8NgCUjjqFGEqIVAcMLcZ+35//F9+fQpHOyx2LTiHaZGbf61FcL4h3UGwHhQRQkkQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:eBvDr/jwahRqMCgfURIyze5mhmwyFt6hKBAQ0SJgtZQ= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:WMYGB5e20256K9gNu+rDfg== service:fosun.sumpay.api.trade.private.agent.pay sign:JiCFJam4G8roeE+jsRnJheniBdr0Jk3bVg2uBiw+0eE1yiLZLOp7LCg2xsRQXzlVFPtWINfmgCZ+nePLisLaRNSGm0G706irT15LnWaU9eanehwYy1WsAFR/0zV18SMLGPyqGe9cgTF3HRq6wc3q3c+EIHQB29fNKLqlv352OEjQR+3KQpcb52gxtJ/nj+ZIWm1SNbhFNq4OvT7M/+HFugStjvzF/qe5x/WjRSiF5kTieut0e8sJ28yRV60m6TQ5BPTxYlxrk0YOqHszw6We4+S55Ee54wKYAToAEOu/EVc85evwDMmu7XpdpjBbthsRL9fgnnowqpFOWnssV1Dipg== sign_type:CERT terminal_type:API timestamp:20250801095212 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:12.923]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095210","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:12.923]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095210","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:14.092]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095210","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095214\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:14.101]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095210","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:iUgDz9KTXal+C7kDkQSVO71FcXQveaEM0mWJJ3EkS0JvkNFW7FSObhQXf+PD+R2QIGn9qA3iUOCZVezr+U0yg3aY7Fm66EZScfXVB+FRJALLjoQzBKdk8KyfspZZd7nzzZjf+DFdiukppb0AOGd+w3AsgCovuII7ITbwcPahLdLbZcCHSVHCO6tTS+ejriHUxX/3+ivwl4qAWTjzIn3+87z4iKdFy8NStxKTkrcBM5T835HOq33kB529Qc0QAfkHOs19cGYBjG5Za49t7HqYNTHe17T6FCQFpBJWm9bf/jg+f9oofCnnsBy4Eu7V90mZYIpcpWyLmzam21BsVjgd9Q== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:SDE/Lw2wtT5LqyNfyS5HGp4wnBIx7gU3vXSNyPdYmLE= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:4lH3/q/PqAN4JA1MrK97jA== service:fosun.sumpay.api.trade.private.agent.pay sign:h0Be8oOlxixTd/C6U/Yo+BtthqUVcUvJPF11UCsWRIUryU2ixgxFV2GNCbY5tNUfnCUX+8hZgDtCz0aszofdweySooItnA4yEI7ntAzSrxrnmst6+i0OHU3wXG4ne+gBNocsU0kqKa6dMziAhwCaK8qzn8Fc9adXGo8GXf/FkZFDHV4DuhJeHDYOYeKhy4d39jj+L3XI9VbscrjW3fHfYYaee6JO2gEHEfPnli6n8Nj8izgYlSBuIaKjKTZflrFt1o2JKUJZ/KQjzAM8OdBEMkMzdEDPTGtjuB+tsiti5dJKvqwtZG57rr1bQIEkg4x3q823V0dIMyAvsI4cK9AN9w== sign_type:CERT terminal_type:API timestamp:20250801095214 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:14.402]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095210","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:14.402]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095210","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.202]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095220","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095220\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.207]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095220","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:1mXVXoEevswtZApFdexJp3uXmiK+0DO52bvWL7q9Zcj8+fpPHRFiswDk+KIPhRRDt/rAZ1wtRIPZPdunpOlmwebmvGW0B3NeeZJuEfgQVtOzwUvYglqjeP2R3/Wn61UDfdpDkUQy4PSu5HyBN2VUKxLq7TzAWHnQWprd77PY3ATHz/OcNH06dC8t9HPl9Jkgh9ocYtiIezVT8x9C2q97yO3SYom/aogGMhsa0tuslLEKlj/jr63ajXHCfIGQMJoYxNFoDMmbJswEi/pkMhcnJMPYPPalcGgErW8XeLcYo9yFh7zw0V1JaF2yNeOhXe6HVT7+rRn9M8PEMGQa7rhiGg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:vjhmTywF4ApoJJSZIZJpknBtU2/uyAqSktxVR9yIeHk= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:+Y8cibkOxdx4vbMpjBQAxQ== service:fosun.sumpay.api.trade.private.agent.pay sign:WXKlmtOO9YvmDlytcujBieWp4BrWxNJja8KEjbwc9lNnEMNAQjf2XVExB6xNa4WNvgew4/3kMUZ4hWXxMd3e3h1SQsj+WDHrfnHyCscnrqWxhlt56vW9r4DJBU2jKPOiH1fLBDrvRLiUlmqwXVAcy4b1zmWn4N1LWIZw3vKgpDztDXPpNbZ+8L2WPlVDTHL8+tlBljgtuKQDSfX/v6jWojU0NVNhBkiLLC0TBsGR1fEH7QV4j8D7P5+yc2WzwYyjln/0MuZs0bk4F3aMAvK3wfDsktkYODSNuvPog6Ea0xuCTBl7cSNjhQx/jQtIHo5l3nkx1uow12gWK50xBA6Jug== sign_type:CERT terminal_type:API timestamp:20250801095220 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.380]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095220","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.381]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095220","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.577]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095220","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095220\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.582]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095220","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:iU6H5lsgKACEfch8zMFrebRzcNb84HEdS3HbEN9VDlUvtcX46UVomB2l8ELJgrrFwkIgVu7v+Ix7gznjkwz3IFVGSPt24d4ZSiuCI2qr5lahbf0aMzyiapDdz7vwas5Dsmlv2KugqlFJ6x8RJupeVOC7owXw7a6lDXp3nu6xQ87QGpe/mRsVWCOOA9pSi6SLDLT4ofNc6HuR+4JXDZ4cRpCFHYp6xhnRArPw64mz0jNX6sm6W6uUCnDYvz2e1AmUpBvhKFGzQpjXEUMhterGGux2TR0KLhy997zGi1l8OgN6ySRP/CwVJ8FkEz2VSlu7RvHj/u5bHcTaKe7BW8dIGg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:tQjvLWjaZd+o6kNUpTQnECzhbzHfLjgPdSXE+HmmbvQ= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:RuHsNtfl8eDKRqRmtHRpVQ== service:fosun.sumpay.api.trade.private.agent.pay sign:YbtDKGYNDRf+3TH+9qeA/wMyBXTqV1pZ2ToI/lGS0GAOM8RUgHBJ2CNsQTjCK/mIexkK94lYB887aIKQB4i/VigUBYEaKX6FrcPr+KXrs2ytilRrzbB2PeJXHxhVP6GW5Iow21JKFkZqoCJXYm/5qqX7DgKDhUmRTTLruTMaCOaI+tfcFD/GyWnqSUQV93aqLLZAxDvR/wN0EUtAeTSWiwgoGSAYd+X0/8HbyVUOn06fyaYn8AyIc4Gnl7Acs277YVHi4/kswbLbXbt0woAzFD9HWyHOnDzhu+xKIc+G0KmlieZ34wC/tNnfYUj8KofywjVIZK97tOOSHXeOyZBrkg== sign_type:CERT terminal_type:API timestamp:20250801095220 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.717]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095220","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.717]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095220","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.911]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095220","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095220\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.917]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095220","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:E3Fn67CRY6wh3RaDXvTUbtSf3eeBBjVo+Sn3jIg5TVvLx+9On8Sr0THrlx/lNBnhibD55dGnjns/92W1L7x1ZOXvuTUydViOtDXqV3u3SVLE6EQHeNG/jQ+0t/uYO0ffFSLoa6JrT6MMFhlMRM6bfNdoaYBPxQ1DxSfNKdvYtJ4jcgOuP5Uu8Xp6RQQNhFavL6FY/ZeSfWJlhgfPxSBFJKr2cxDK9k69AsEbCpX+oGfAgs7DSJHeuoDTFuAIXeRN8PXUMiqnH0PqLoRg7d0IZHNOS3LbHBWGyJZy0ZR7+AoPzpOQXnWIOWNu6k9j6rzdX7dItRv0OPAEYvdudEl+yw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:RpPZqBzX9GofxhZV6FYP03WG828W9HOfH+E3fIsSyrQ= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:pPKBPzcE1K08A6dVjEmrBw== service:fosun.sumpay.api.trade.private.agent.pay sign:OrDoEqnMJ1yhGwqC6XjnEYInw+iGlQU0y6un8dYE+Lapt6YgtwQAlAImfmnyXdWEcznaULrFLOk15+0iscrOVv5J/medYLStheN+MidahYHtQGkrirQEVuYHfhh3nVxJHrF5kzzzthoAvUVmsQJBN4l5OrsCP2isdjQTUBvMBzRB0cCosFFGYULqF7Ykrn0/ck3m1T2syTMyXrjIufCBYwzsm97bi55XX0xHNl0DMbCU6CeMRzaS0wIA0FPY4doSO+RVFxEzTagCAj45UlBBwf45iC01S8ilss4YHFboiB66GGDHETCzJBAPf1daLEo8vmJsvDy06eK/U3SnVRXFBQ== sign_type:CERT terminal_type:API timestamp:20250801095220 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:21.064]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095220","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:21.064]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095220","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:30.645]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095230","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095230\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:30.652]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095230","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:ziDA6XX3dXK4iMY9GytKT08U/GI5uCpukxi9uHH/oHvRPaVGQMLajQK3XZt0fRwxVVjglfCGRiL74sC+CrKXEroqmXica1sXaR4fEjUkHt2GwAEpb/v2LMfFOrERI/+iOMUMtsN4mxKIMM7/GRhOgyXJF5/R0jV2GPURwxSt1pLAHp9UuTNakLTxHZyCV328tzY2ZUtzhYVUQfhMfwnqGOQiUmWS0DNiP5666O/BAEXVpzA9BwSJEtbmxF8kNVtPEYrx7mcoSJHRsXHy4CfxhAWSEkfFgivWC/39NGKtGt+pZThsHGJ4ZxdiG+3RfTIT9h9A7jh4ER3opdm5f++mYQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:4hXaI9uTrR8t0H5r6TeE0e9kKYrcSIGIFyXoyDbaClQ= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:3QKdmAAY9mkJoubdTSVkXw== service:fosun.sumpay.api.trade.private.agent.pay sign:VyeHiQ43n60OxCs8kLuRBmnImjSWTpJhYghoWFB6y/eftssa/JF7mHbQh5JUxZbXwhWaTehTDev/TQWeI/nYrKOcsMq1uMBwDxRHve8lbGubPhR98WTXXOS6X6mg3FyqjLDtq+qKlRecbHghGuoctFU8V2B6EeQ6BMY03Z4UXEDObedmYlgHaibqe+BUh9xOdmnNyXeaSnELstl5frFXwu+G5k5em33QgjIUO5LNzZlg+7TKyyiA8VQMxGLEyl0ypfACmC/8wjX/ukixMfCn3oNexhFBq4VU/i4kbl1qm7Rn+xTy+DLs+RKe1txSWbgaTOwCg06s5wyRwY1jpdRgfQ== sign_type:CERT terminal_type:API timestamp:20250801095230 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:30.849]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095230","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:30.849]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095230","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:31.240]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095230","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095231\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:31.245]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095230","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:Xgrc+Q+VIeTXCTa7l8lVURr783pZ0cBKIhIQlfc+bF74wYuqs7MAaa3zf5z1bCQFV+wFBVG5130Rt0RpIVFpjmOCLVES3XLN59jWjsu1Mo/bgqeEEjY5/OyMAdS1MCWgdEvrbgWLZRlML18qFEqfT7x8uAYx3r0TF/RLFDoop/sgEjUYe3gu+ar9R9PdAJbYsmyAPWI8g0iNNiLri2wvu1SBlYd8eSRCMYZONZO29/0p/X72fiGEVWC/KAP+HGOn9dgwEz0l0RSQQzQ0HleBMkDLXon8Tsq1djqIqzwT8OMdR5F9ho/1Upt0G/LzZ8PmsgSp2+AaZUIk0PkQC0QTuA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:1t8T8LzgxoYXQgq97sMvR0OfcmvKo3TD3tDNy1Ogy14= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:j5BeqQ8WxZGK61MZV/mIQA== service:fosun.sumpay.api.trade.private.agent.pay sign:ahfCakZsrmCbOBT1GePBdZRzH/AubQ1v+oAjanOaMKDTN8xEc4ktjhmEQq8t4wte2NHS94ILmAigaWHyhI91oOZ/XK7tmYoJJbsmVQgRWpqBwfJGEZijuobHeWVzmyIhmc5OOkHCVwtkgfxUbmhHuf7Q8hrdkNCLRxQJmefbYbU1CJZ2QsSRWf1/aHJN38cGf2O9hRVSdxHsOPrc8AMzlZoUFNxEQgDC1Q3jg59jK3NLr8L1eOB0PZK9h3K63ct/QgznKEdtJMzakVmlTmO1thYTbYIGMHkiWWsidZEoHJGe2ZTIli2S8T/UuZ7Zmq45w63e30UT2Vcq4y0HvN40Gg== sign_type:CERT terminal_type:API timestamp:20250801095231 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:31.434]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095230","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:31.434]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095230","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:31.777]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095230","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095231\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:31.784]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095230","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:I4QqoNmnezGsXG0URJ+w5akaGGc4ATNn4RQICnK+J9AjY3m1qC0Ra2bbMi6sVtTY1KnMJ9zQK82VZPFXpRsDRq3GF0BfNS3DMOGFVelQ6KKZcvZnT9+OV9PC6SInb+LYr+YzztVKTrLT3rHmoQKIhHYzvxMdp1VD/ScpUnHrtAnBlZlUoEFzS3QZbCYiMNMpkeUsvaCq1sa4YkNQ1gX/7PiUf32YFQZLUIgRTn6u8B2K/eqpcF8jOu9isiyxDDVjpqnCdQUbclv6J8yg0aXvP8iEwS1/7aGByuFqFLBWGXUFrokaXE9cgNNpeRhkz7/aaLQcUxWIS3aLcG2YvmmvAA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:iD8G6dK/puf6rfBHT9l/MmNvTRC6uMcHQLWSU++XVBM= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:tMG9p2DbD00yXR6cOtRC8A== service:fosun.sumpay.api.trade.private.agent.pay sign:H+ThA2+F/EYpak3QgWVXGHDVhY6weomUCE7qPnHC0H1cj9ljt1wju+UVGiVimD4LkMa5KIUTQg4G/qlcjgmCUQHkZn1jjdyF5dIKOrWtuKgBVUTgtmfhubbFoY8bnKhGJH5LaFO4FoB3KLLRIQ0093JtdTsls82ntDx8USVz+dVIbg+8576q0PNOX8AjPrI3tiJTV2rgNgm/t5h1stHK5TZoiR9AlZM/Pdq3Il7ARtKUjz7gQjQDnexqecV9gU0VvG2yf+Iw4lhZRDA/sin4GRQrfeup7IyJ/c5gTTthfv44O7oe1FDJe1mWkHSYtYEBX+M3PDqyYEiXn7oAlPuWqA== sign_type:CERT terminal_type:API timestamp:20250801095231 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:32.038]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095230","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:32.038]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095230","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:40.564]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095240","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095240\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:40.573]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095240","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:7UjrH6rgJcGhTL889IubeGpZgPAAecTBVHZDoM0coAAuixLiWPQAASH0KEo/eIkIFWGb0448sBtXd7tGplqu1oBAAVAQgDWdxnRGAjz+F2aZTN9T+hZpBhsfjeOOgeKYd3YddrEEbXjXBIcJ2G8MuYoprGw31j5F+TNPf6GDTTLJpp8XSww2JddCRIwntVFLFHARvc6+c/5RVmdggc9AlUY7Igx2jNJUn+J3Hc5R+uR3Y6xYn/e6k2ZLgpqN2B+tIoKCYOBDIjljWoss6TGdpsyGQPK4WExN/YbXn6rVSzO/OU8BGbjn2yaYkYinX2nrQ7p19A3d8s4jhwLrek1jtg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:bW4kqqfBTQf6GFoFPxRsJ5TYvNJOuPXBDsdxLn8Oyl0= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:eZSPNys4w6vqsubdb/UrbA== service:fosun.sumpay.api.trade.private.agent.pay sign:FkPG6HYRHFtPx8VsE3w/w9Tmyd+1YrQo7CSsZKW3j7VgROthTuzgjvtQO1CRi45FDzI6aNyIJBC8apOvPl0qYO5MAG4d341IEC5Zjrjtbpfkioq6Dj4bPvFD3GgwhMcWTbBYtiRj4T75HKsOZZwlHjo4JTl780pIhNkysNCg/JiePItjibkQ92cBisXqfq/ewYcwBTPtE7G0hC8gXSdiLp2GLzE4lQiSLC0MXig5WJDRnmqvO3AyItB8cAYOGonrDLFpNyOgRSCr7BBcq2DZPWWT3Fk/NmHrZZxBw05opVpkWR4lgVNq2wdVNAef3SxKvtUp/mYbnPuTT3AgkgZ8cw== sign_type:CERT terminal_type:API timestamp:20250801095240 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:40.854]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095240","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:40.854]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095240","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:41.193]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095240","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095241\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:41.201]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095240","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:SrwMRIHDEVI3eVrKkE7uRG3wktZFaK7b7QIqgTaroB53maCGDT/d89kth6zqme+FPMmRqadza4+5hZVgBqjsEmuMG9+fO7oOCE1Kfb2I0UX1bX170eFp9VHVK92r0cQX4ZzCxudgrJQ8dF8uBh2mLNC8olv1QjMWllq8UgDFNQy2uOGTy4rJoDavkw6Zi1zNLuV5dtxv8SNfVOdJUvnhzOKTZ0Yb40JYa1lRX3yj9RS/qUcCFA04smWzWe6mdA5beypBml9TpzYmFRPIy5MWT28GgjcawYiP6J0AWNRQ3PsIuzOL++3IOBujZ2wpojpROT/GKfQx7NFLB3H6Cm2c3A== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:knuQrhnzElHqdvFIFral1vj1QStknIokyZoSl/3Ks88= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:byySLMcXJ3MF3amBSC1WJQ== service:fosun.sumpay.api.trade.private.agent.pay sign:Cj8JoQdkPLd5Nm6b27M20Z7H0KYyZ3ng9IbkpMZb81OKUa1jtI/VoNfEbh8phIcOgWq3gJDPeAjsyM3w3hqv5qefjv+X1gzW7SQDkO3VWFvwrAtlrPaUkT7+4cH17Qrm2M5E+/KA48j4N+Qn4wfFZPfnSdjy4eocAKI/qMeg+4Aa5DZzhdzcNYiho5k0GapHppW4F8+mW2RKBVZejzVOBWyUMn3/XqMHb8OdarLUbErGs5L4vo1QDQfmaYkLq3xWkCLBCCS0kKglcPDtx/j9EyrrgJUtl2JPzjg0bMtQ0IRx74ODhctdoYPrkjMzM/1yGXr/HL5n25UEevg3xrtjwQ== sign_type:CERT terminal_type:API timestamp:20250801095241 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:41.500]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095240","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:41.500]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095240","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:41.858]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095240","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095241\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:41.864]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095240","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:S/WFCgX3XEO7YLY1ipK9d4UTbVvm6MyTjMeR6ntvjh+rKSOHw+OQfaD1R6dRGRHkd/H/AImFMqlk1dSD3B50gJH2cqqnD8NM270u4FNuYNsejPsaP4yvcwRXR1bbERDwfqIwnF2lKjODBY6+piOAzNhXScTMWioGL5OpxyrMELXs+DWryALgm8gtL98nHsSRO6HqHB1lqmFcs4L+NsgLPOf7B+nJO776YGbtwRt7GkS/IcvouE49g6I9n399MNpeBCWYTcpjbOEiPiQ+fBZ+qT8KhAr7iMaQ9NUCOAXwI9EHRvyPQcDJqC3AEbh2HMhtiILBxoe8z0DxKlKZyqR3qA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:7mm9Ze5LSkE8fjNmf421Gn3KeYUp30QXrTKGYLD4l4Y= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:otICnOR/5uh8UyaIShz6Wg== service:fosun.sumpay.api.trade.private.agent.pay sign:Q2edaoCQJVkwh58wA5rwROV15WnjkD0Wsi126Hew0fmVtZf0qCy4eSvdSiPGEfCTFR8EbaCQmsjqHo67ETRzKlPgaEpFdZ1CEPF1HEULOdtr0UU7OzujQg8IwuyYMmG8Ou1pf3ZHZsxSRbmwFjHZkXbnEcWxCwTIlSa0auOPyIeexaw8fYotHNIEzOzfcF14LLJin7ZWG1FMFlmUQhP/0T61jSeG5F4N/Rh+dlbBGcd7m+4edd8JTda6km3PYXTHWhCjwPOf6YxOefpLsRy94mBWMmxLs71s2U9SjEO9WA11bVT72/YL01NWH+SUK7grhgY+ooMDZCi6VoT4HfBBtg== sign_type:CERT terminal_type:API timestamp:20250801095241 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:42.037]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095240","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:42.037]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095240","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:50.569]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095250","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095250\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:50.574]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095250","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:Y7rtSm4TIBct+GdxkuRdQsmJcctt7K7JPYTZHJ5UN3047dYLHeRFl/6zgzC7YOatM1OeDwiXCvQJGpkUSBqf8xmbbqTR7bWb90/fkEpxXoOK7SVYvjRFPcnCKw2IAC1BkVYnASzE4qpe58Nf35kwXXEShF+xNGLqrqbPq31mujFIOo9akVRA+gNrJYj+6ljsSYQ4NVioZZ8s5/EZAUkWOAC0O6gPRN1TjKDQlJx0ZQ34Jf7e3/doP5Qe1YmFj+qEp+dI9ryOovFC5p/KVqvP5wurdAkDG5qr1cjcYYhnY0HWlUuDaCEof9Q/fBMfUwiFSZmSV63GhQtHKstGeh4Qtw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:0JboJs9srXVzBP9+QXpE0C3fGm5HHxlkLlqr1bU3F6o= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:rhB19TFkMmN7kk1J49m0GA== service:fosun.sumpay.api.trade.private.agent.pay sign:CQJn5SbZeYRlLXRq9GdXq/+V4SmoNa6sHeZomE18ReKindr00KJJlefvm3NB3ubWcj9yG2IYav2GlLbFpT68uZwBwrgGOqjeT4xbzfc74x2M7phVbAo7lHEuT5vFoLrUV4tmzgnyUhRM/O5Ddv2fUcekFr6CIU8DXOe3i1NZqQqU/NvbSeYilJ9NzKCoLJpNjUZptJQXpOULFJ5byee26SMwRqaUsEmIxdE85FvtudJL4XxWNddupshwXex/IsRNwMnkYN86aaD1P/HiJBTIwU0b81hI3G/vBf/IM9x/VqtzetxW7r8ac1tfIM9whb0wq8sHj0U/ZKk5fpLVVV9OmQ== sign_type:CERT terminal_type:API timestamp:20250801095250 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:50.783]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095250","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:50.783]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095250","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:51.198]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095250","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095251\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:51.202]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095250","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:2BBy2lm0OFb5xu7ha/RQhextvwlWJqnUty20YvsHr8UyDdQ5w2C6RfHIY0CxPaNg9b8yyBS6g/QINFb0mmBFQUi4hafNSB9y3/WpfdUi5dS2otHgx5y9LXFGVaDzy2WXw89qLHOgneATqDTbR2ElAiTGhTCF5J3dS6W5fMG8tUGRdVASRtIUKfNphitS+WVfk8yMuQgXp40X5D4Qrh/Q/X3YVnpzdZAileA5Oq+/yXLM9nfLJOWyA8tbul6T80s0+frlSuNqW4MFt3xFCBP+ZqQitPwRxjFyVN5LdIqwws/iw6Wd7tkzx2T6P+cyBFnIbIck9vmzZlIpZ0sWCQCNfQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:GXOvf78PuHZ/HCJzkA4ALy1ZZExoSEZ4f3zbLN5L6TU= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:9XxVILzm942bJxdZq/OwQg== service:fosun.sumpay.api.trade.private.agent.pay sign:ExYN+VdmH980R/fIWd/i6/5LFODWeK9v1uem4EXPbV+1fCDpfYd3/xyUhqkytR51SK2IYEXuHCmTXXVHcx7GMb+tE/fbdU3DW1S8jqPZW8tpH+kM9ty6EqRFp2cBr/18QO0FernnJnr33W04QYpVvKeehe4Grj/E+xylDa81m+yVhi/ZvN8a4Q8r1Bv9+DrWtW0V/zeYuzbsC6ccSYwDYuo3YyI/Xph9S4HtydEK7Q3b5soDbKs398hDo1VwERi2f0xzbI9Ig+g9Flp2JmoR2F7dm/Bqjr2etiL8sPzyThTiI0u4KocTmE6I6UJlvVwzjHYaQXmWW7gPeQSSnqkd2A== sign_type:CERT terminal_type:API timestamp:20250801095251 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:51.577]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095250","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:51.577]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095250","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:52.023]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095250","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095252\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:52.028]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095250","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:gOM1ZABSaGJekpkAIyTXPQy/U+ZO76Vv0amVWyPp8vkhBscM88kFpI5jX41qQQT4Jx+KgMRVD+UU6/Xrue9GI46pFd7UgLCtvEhdSingyxCqmAlhiS8zZrWAz0KvyUDSyFuxNDUK2N/XnPYUsdfFxhRNso1ByhtR110VtZcbVVS+o0MiQqRGb06e4RnDuhH57Y05a5aNLfQeC4xEMB/n3bwcmCah+WUTcxcK+BeB54XPb1ponUcSeUXPQlU+7P8KqEmDAAFzcQi5p+CZCoJDJKz6OkEKIVtkNG1FXc6i7ZgeHQ/BMGzGJuSg7D9f1Y0dl85Y09ym7PahlulPnOqUxA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:IuQ6NlZNvUQ9Jg8fJ3Pe3pV+xyS0gijb5wxgsrkTGv4= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:nG0Y0X15K1NUkB829JCKpg== service:fosun.sumpay.api.trade.private.agent.pay sign:Cfx/WAnrmf4rC4Ro9bAXU+6OrVyAROP6tEnaEUooLqUn4Vy7QHXMEUpeqyVFgBxFszZ/1PL96WORNR2/J9ixAGcGqKnPhlwXuZBmentz/dhWawRcVN0dezPp78zbpymoFssTmwOohNBpCEMJzMf01bTAS0dtRltrqe0URSeFLxe7oX00Vin76V6AfQhC/PN3K66bP/K85Fc3UR9BP44O3SilrrlD/15scDXykuHpSa50T+fgld+562q+TaqHQVh2J0G4uiljRokiKom6bR6LkqIL3uxWuOBL2i/v7KpZol8C5BBb8tP35v24cJJtL75hrhdYIyucwjnhwNCbk5Nqkg== sign_type:CERT terminal_type:API timestamp:20250801095252 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:52:52.213]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095250","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:52:52.213]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095250","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:00.794]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095300","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095300\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:00.803]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095300","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:uVzmTyVFHoM1r+ngurVWXeOm1qNwCLeZRVNpMF0yecMC+Bk1OFYe08bYQb2z3SZCKQ4Re6Atxoxdd0qNeEeukwBwTJpFwl0hCcn8B2TYCCOsKN98veR0CYmdKlST7yj4AfxOaTk8mnIo5bjKGniubPaleyU3VLm462WMOvLdRNDUP5BKKv6STR24kTzfI1x9G7mp/UmvgJQWbTC8B/lTA4E6VIx4gb4nzuCmc3KvTJiiH09Vz/55IF6K25rqlyFnluIjFyXVjZM4HEaKNwuUp6Vz0pXzDxaFajSySgK+hZdQn+HjWIYGum0x5I2HIcc1/wPKxIYZj/UKHi7jpaRXBw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:mmnekw/wRU0sfU0qlROyGpAeIkXksgr3LLfRvLbdW7I= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:7iINTcwWksgKP6CtLau4rg== service:fosun.sumpay.api.trade.private.agent.pay sign:ixwtfFqNyTSiAnhgEJAZfl6tIovLYF6zsTPpVAIRd6ijZyAy9h2wNKum8ZcLq2GJf0s1pRWhWoyzvjee1N8LKjMBMMnIsjy0x1gFUWqaZ1tqfrTch3Hi3fv5yt14vtBpGTkjoC240Dz6CAhfjB9YsKqNjrpAJUvEP3/FTQfv0kiVqY9WLBzvfdTwnzO1BnjHGVCZP8ShrCEtTbv6nmRvtZLwHjUj72btv7daAyEfo/xIi3wltGPMpCEx6vBmZvC4QJPHpMg5qji80qZjwC39kAELo1LMeRIcM+keGwx6xYwFKrURMqZNNADU4pKSubuNYt+IqNYB2b1Xa/iOF3mSqg== sign_type:CERT terminal_type:API timestamp:20250801095300 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:00.989]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095300","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:00.989]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095300","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:01.509]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095300","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095301\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:01.517]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095300","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:wRYW4REvkMhMaJdonuor0JFQ/4W9lmfsGwZSGWzmmYRJn7YGscQ6CApXbW1614ZGdajUDEFJM+z6KV0EYtK5Gm4rl6GD5yfQcf3vOQjFOXh8P0rwVn+AcCw+2vmpRR8yFvO1KkW1oIpRKs7kgFTyPs2bv3G1fAE/OfH4UudVCSk4lNIzddBnJNJ+fN0Q3CAfHlDkX+R+XkoEJtrlZ8b4y92ONkc/84yFEOsh5ff3sZ7Dp5FWSAK+jsYfB8GhNaynK7wvK2kYnZgmu0O6PIsaKtzLXxcmnGh71kAPhwLLZ4nqd2kU4Fuu5kr6+a2bK6SNCYr9D5CVTp7MeOQiLsLl3A== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:3fOU0XJqHL58R4MuOllMwbQyT6SCosp3ny6I/D3M5e8= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:rO5uNwH7+q7hN5AkeUQbZQ== service:fosun.sumpay.api.trade.private.agent.pay sign:gugsg0A2yLolby1D+voy52ksy3or4QsFCR8kljEY814LSsqPNbjdrAf0jYb75b2439pvZzqKvg1WT03emVglqSQ3Osh7XSxyPsZOVyeft0W/7DMzpr/O8BzTGkKzaKhL90eQrafymHk4VX5q5eYXDaN0Vaef51IxVh3CMnJPcAcWcF4iMC1RYvLByI6vrEqhk4FhT9umE0T7OeCb4zhrmnKuzojwq6u2TtMpkg44u7EUgylCSbP5Go6SSMrgewRIypIWCrSam1wuACIgsMl1Yg6HrxVORIbRnzxyvc81vAVlAH1wH7GykFx2tjIdxaNezUGv6n9fCuAS9IJnHke2lg== sign_type:CERT terminal_type:API timestamp:20250801095301 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:01.685]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095300","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:01.686]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095300","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:02.238]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095300","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095302\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:02.246]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095300","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:UK5UUqweLsHmz5ZeIvGTYiBZdqizrvcaLIREqyaG9nPcrnRxXFHISxH8a/c+JxXePpxyLdNXPzgoB4lkZUIwl2uCVTpC1/to+KyHDlkLx7n6Z8LszKFyAng882FiH2HmL23HxP3pcxdFLwCHRm2AsoFQZF/oO+Z7t7oPlPaoKTNHYN9b8hDYtqslm9Jq8/ALNybtEFN8d52rKvlIDAUtqOFClLMThwwjUCUTHUH/DzNLiuSQdenY1cxr2QZfF5jONHdAKH7mR5H6aYOmcMWrooSsAUFSAMAriasHhRlztrV7D4EVBvHaYyjhER9Cn3HvLBTJtHsOLXX472V30V+5YA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:iq6wCh8JkL5eGBAWDLJQCdW8h5uwHZqhv2Lz1LDbkUk= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:9aHQjU6zduHl/htGvFDqhA== service:fosun.sumpay.api.trade.private.agent.pay sign:Sg1nsWKf/5mpfxmEhUcaeKdRttB8gvq1Ds+WMnNYdaRA18bbpJ/pHp3SbT56crh9GcnCXIYPHsF1A6LFcD7/fxtg7B3bLictH6CNyIpjtNRe5qGqYxw2JGUXakqQXictvpS4hi28o1rpcUXwatrebKFUV5IS6n+iys7pJRlIy70MlVRfCXbRk2CXkTdmIdxdLyCwXL9FXRwjdh2QDjy3FSnV7A4POqSkCewRfhZ7aU3b8oCfQ9GIb96vYh96CPgiLtlGzEyIptyIFEgUeQ+X4kE4uii9UjSeEi88q8LwY6V4pnNyK0U59Ph0UE42earWBpKlUhvibgdA6h+iXVebyQ== sign_type:CERT terminal_type:API timestamp:20250801095302 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:02.491]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095300","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:02.491]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095300","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:10.385]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095310","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095310\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:10.389]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095310","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:jRZ8zFhL6+TKYQaEsV8MO51oerdeiIPIWtLBdlvWsJfDbZfk4PTtZiRdYLX13HgbQzUiZH+I0p37UKGutPkJvoTFGa2tAeefExO0S8R9zvDdtQjXWidoK4ViQJ3ahbfSE1eUS8uOvhRVkC2+ma1XYS40bUloDtJSOkRrvOmqM5cmJuDkLCmLSIWVkC+CJBvMh4Ptl+cSmkH+zm5w3s/WD4ru2kiucmN3SeHC07ZUwe1Wq7Vh3igaTQcSTvUbjGuBOxPv7VrqnTpiQsPuyvgXbhbw6JCxdFm3vxkN3Ysw6aGx09iuaJt0WDA2Nnr895xzEyKuaKF/OfIDsvdZX6zt0w== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:s+/fk3THRcWijLh0wEEQsbq8Y6WVe01lqTJNewrnWKE= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:2aZpCrW+mcS4qgx3/rMnHg== service:fosun.sumpay.api.trade.private.agent.pay sign:mVBtGMERS9yurWJkCsnVvA8ppKAqYOdVa6BGIyajmVsg6T587ijKoQWUQr6BuiOXNOvppBlMEKKGPlHZcVyVvyQQ2ohA4RWpLG8MEpRYXpI3aEbr6PjSIPFaaja11wVvB+lIFdcZD+ws/4qY0s8MOPLh1xbf5CX1Qi+ZoVB71qu1n9R6oGUavhlfIXrduNMoI4XO7yoUDWV2RBUt1IscFBlB2n49FwKttTn1K6VUImb8DT7HpBHqwbB9urIBSEHof/EJNL6IQgCzWIiQ6ngHrA90wm0nfbF2SU94tNQd3dYYyrNiCzkpnzCqNlZNqyB2zvMzivvd+DXxVuZNDtfsJA== sign_type:CERT terminal_type:API timestamp:20250801095310 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:10.639]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095310","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:10.639]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095310","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:11.568]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095310","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095311\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:11.572]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095310","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:MMYMTYMnBU0bIsB97dMSYq2G9CdsTMi0tJNUJ/qE9GhXW9jAla1FWHPDrMVVDGlYN1cC3OfXS1WIXdihXPssRu4s5/gdEXE9v0ICud5RRXWMN3CWMigWtq1cpvdritO7m7GknMbNxhYbnVQu4I1Ot03bGY0VdciIBWdqZLgO/Op3KrRjLCL0s7L8HTAD18w8aOUcWJF7yiixy+EOTyCQscGtuLE48rrUBnofkpyMYnioJWFz8PoDljMtknTmqFhhLwV2ryy40sZZUOPny8jFgaiqaKdJCz+XjiwPThkzVDPFd8Nn+AoNlS++7mjc9Xf6thRGtWf6hzrzTwuYBI0tKQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:v/FFLMhQJEuqWPv55BNpYSsEvp+uMm/sSRyCwM9F0DA= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:s9Xj2QTJIbaxm2veAUwwqA== service:fosun.sumpay.api.trade.private.agent.pay sign:V9Fv2muvFKcB6zaVsBcdAKUOmceUDFS1t3TIqqvMYE7treiW9YSpITKMYnXisg2xWxiYcK8NZYMBmRMMf8tcpXvl5iONV6lCO7wZrsJ8wcXZXtKpjcuKT+qlOufQShdqDUVOI9UuDK5T7OgGI+fUFHDGAGpLLPEcF/BWaaxzXKYBlhJl63pM+I39Hs4j7u82iCbLYpekaucXSJxqt2Z7WpKY01TIlvEcF2luVVgGDTd1nLG8SNMppxk5/9Ggg34q+fre+NpmcnE8CAWhDpP6zJ/B/w08xAZHgEjIW+yTVheeQ8Xxj+oYA7m0Ekmxga+AbQzf3+aBHvJ7R6RH6zMDMw== sign_type:CERT terminal_type:API timestamp:20250801095311 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:11.822]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095310","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:11.822]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095310","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:12.454]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095310","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095312\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:12.462]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095310","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:E1x6MF05AwEaxbQ9HbgqFVkPye6HPhIy7HEohMdSxNebz23f/4fv1FDA5lfbVHDjaa5w4s0RT2OBsKBtejy0dtW0zQLuJm0UyhszJuPbobMPdMNnp1l+cbIOEIMri6TFQbD+yEz3K31KyWd5/9IqyoZJtP5kp90JW+Qw+H+3lQtJNA+3z78atWQIayDkXxNTNKgs/CMeojA4mLtMRHY7IHtMl6PIa4CtXlJONCo8qZyOjUSzl6vV0D+Rv9OzfdMqRsuFkwhx3bBbFGtjaQadi3V83BammHcTe1Bn13X3mrEpWdnQRfxQmBm7LySoMGtrAdh/WHyPbBLBa4XxUoyyQQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:JW88Vt37Za7xOLxCXYqd9avMTjDgMHn4sKpqXBfbkXY= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:gJD/lU9atkdx6ZMeVai4Pw== service:fosun.sumpay.api.trade.private.agent.pay sign:mFjAqHSfK4yVPxtCUgZciKJPTiB6gAhee9THF/IdICkImSzB5EQj3ESDW9QEqn51R+PrFMYcafm6Z2rRTt6ouecX2wz94sgYIlV+VB0g74F4lJjX0THmWkof7tGfdffPdoKMh5H7JXqXVZYvmMWJfB407y/nRM6N26SOeC3dC63IuhtQ9iUVlN2Sbsn/EzdOaFFdy7k1WJGttnaPteO1Y5zqQevBjELM9K4tT9GozFFsHMuFy9a3DGLuTK3q6hw05nRR7Zy6kv3fX06/akIwG6RloDlVaeqm2fWZfH+b0GXhAIiUZPycgpSAtXKjqjlXgLeRYv617rLUU/cUHY/doA== sign_type:CERT terminal_type:API timestamp:20250801095312 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:12.708]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095310","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:12.708]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095310","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:20.461]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095320","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095320\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:20.466]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095320","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:S6hLdvufZZ3EX9wrwOp0RDGdzDgfge5SC0/FR9RY0yMcR3+gHVEnttxToioishXlb1KPvKdaY8LqRKFFpOiClj/axOVM2+x6ttxWLlH2e8/F2yQrobcQCY4ypQyXkwNrzARE1iNhfiSQ6wp314pylNIxDwK7vo+3aVswGDj6zzQ2g1uOSvu79RpNzQSTTBDtR/ICndOFHi0TVRNbcAib4q7WtTPXah/WtJsMyLCJ3svk4WZxzDj2KFFq6rJq0jILanf8Fomb6SgAkXx8+33YlJ2BaCh85wa/HsvFX59bLp3jO7NWTBGKohmAOoQiqI5HnDJjadp+U+RsK5imlK1AiQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:amSjlPeN7tv7XK9RgCEeZ+BbGTC6GkvUnrn4SvJXvDE= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:49yNWboDfYpIfmU0sVy7Sg== service:fosun.sumpay.api.trade.private.agent.pay sign:fcvUxCjYxaVDWmAyuxjo3jNylsa3JEI/XBpgjeO+ksfEGrTpw9JkBxrvLwDaof9OmoWcjFA9teBOO6XB/AZsRbGtIJHXbINHt2iKxOvmlYYGrLG8/TTwuOn87uI+cyqtnLaZquKnctlw5aIceq/ci4M+H6Qi6wz+2XB2g2dWjs8qN5nUTm745QandG9g07pbKW2Ay1qxhY3uPxcO+X9FrB3e9WmCy8FUGDpDl2ouibvAMyAbhjB7rhb+my9a4Ptz1M4T3Dyt8yuJcIHPl188b7XcURlmiBIzZuCN9BsYa5uHFyVxUvLxy5F3AMC+kNG2FsoAYtdWsOWNnHmKpo+c4Q== sign_type:CERT terminal_type:API timestamp:20250801095320 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:20.669]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095320","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:20.669]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095320","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:21.118]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095320","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095321\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:21.122]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095320","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:SFfTh8wdC5d+gigr/9lL+Bh9I6cs4aoRhXxQdmsctAZvkiTEZdk1/ukAJFy/HqrI3AYB2goMNhgi5d2yxW9wxqqGQuy/gnAbDIiZS3Eh9L69/bv3G8hIZFJotfkSXWnpWTsMXnKpX/uhWyt4updYe/xBwuwqGFLD0wEJL9evPQv5u7R4W5rBP+d/csRRa2/tulMcxlUFV4otoD2sQFHLw+REzG2y64Sa5PSTokykM2n+aR1vge+Ctjs/5MXRgBf27PE7DQh/QwbqTPOEeiRVF2Fzw/p4whA9fHe1/McUSPKVph++X2KLnoxyV1youfYszuxTjjGY0WoRePxHFA+uPA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:COt1hHVLB64h+Z7NRaCmugyVF5kKr3uwBWe4Uv9Taao= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:BcaZlk7ITV8/O855ElCLYA== service:fosun.sumpay.api.trade.private.agent.pay sign:PBnskboac5CGmU/WeDl3LUa4ir6mmDNgEvmrqYbrGMsm8KaVN9SE4wA7eSDOpUZm7Ms5RtkI8Djfsjc1qqW68yIfylPx2DhUXYi3MbHWmRaWoLXvm94thudSxkB4KnB5JyzIO9Dt1/izQkMLdOXJjlRcmToXJ5CqN6UCs91IlgKpZ/CajF3d6VNgzzL8V181DK73Y39MbSC1Y1JVEIwe2UxTk1ALHdgH/HE3HTPrdh9NbtvVNlfdfnbrzEx+B9XEj6rQSkM3cLb1Zi0HNpeE3w/thoE3oPbabaFcWxQmzE5Ble+VuQb26NIIQE9mgRXj8Pu5qF0fPMRsc0elTlrrcw== sign_type:CERT terminal_type:API timestamp:20250801095321 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:21.438]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095320","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:21.438]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095320","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:21.964]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095320","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095321\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:21.971]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095320","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:cJI9Spl2difqdM/9wA9xlMRgFCp0+icYiGTKa5Eb97Aq9ieIlsWzLvXuK0ZI6Tfp5YQwERWPdNBI2naFkR/5OOD90bMcKg5LScINBDHndzhtrRuhA6uesm/21KP6VU2pYCc7z0+T+P2UZY74AexEspIHXDJKDiHGrnbzwPjrWwoKU1hSqIBIdcX7BqIeL2Z9ha33LD26j3bv6Za0wjby8s093IY0td1LCYVF6MH9bYyTqR9nhP5YzrXB7BRY3ysu2dYiZViWCE0tjSfw72+YAxDMKVgxNOYx9Q7fH0c0gJpJGOExD/CyOK05Y4H61ISQUznkrMpdA/cn5MxEc2pVgg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:yOOOiwrkKMMylApY9PY7Ghe8yDWPuNMOaYKAlcpBf4s= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:jzdz2kSfUM7SKVx1Mak32g== service:fosun.sumpay.api.trade.private.agent.pay sign:buVS3sapAB+FNFLZv596E1G10wRLed2sRwlWEnNWsbkpfb0s9AqZOes7Gib4XapllevaWCljOhyAUq0zqQf18vf8LDmis8FZ67VcvSD1sSkVOpnItCWMH3FVkSztWNS7Xdupir9F1Wt8+7zbdKepZfCw8hImEHCPupPfxDLNBoU7Kewsfie32A85WkHnGO0JfXDuaQLaztyz8jBWDUAqqpG7S6ucthx6t9MumDZxgU5odzVqwSvpu++xeQROQz1K8cz7506g1PuvwhmRaut7xkpjBuZy11RinvWzOhHRh1yiHnUDmfzAT4m5xY7Bz0JoH9XRiSYA7w5zQpKMrc2jDQ== sign_type:CERT terminal_type:API timestamp:20250801095321 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:22.185]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095320","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:22.186]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095320","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:30.443]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095330","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095330\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:30.450]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095330","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:YAEAGpazDGlO0VECV1WpO3Q81kKf52hey037tzu2cF17RLg+mGT63Wt6JGy2OPv0J/M13l2nqrHgeSeVw+ob11eC6r+Xoy97jzrAahf4UNRBQB/nU/82rEBemPwM4Voim/Tv41fH4pMQxV5+sui8IiTpsCB9+tUDLnmhJ7z/2Su12PZX0hNBqq7cA/eekJJ/77sC5JWAND2zgJMP+4qmuxRnlI8BTOkEivGC+ssYpN1JU2L3tCT1ptBTJLJcNzNPIYC5sCUiu6dtgWAShHxXDIyy/lUylDrF2rVj0ooHcnvH0Z03I28ug8+xyJN8oiP4A6IUxaZqEmgKxg1Yokf3QQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:a/ySmeifvf8AtVTAqsfmPKFdaxh1L5oTtsfwVAt3F7Q= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:P0YmjXPNRizR6rMwlxDXQw== service:fosun.sumpay.api.trade.private.agent.pay sign:dNneemAngNQnhmEllB2zlyeRHQAC3I3JPmUOs/giJNi4u2TWSftF6pAVBHu6l7PzKLh0VEVcFWw1631yR4eHOizdb1M0IgOO3NJFUSW1Ywn/yEsduSFEYkT8IWk/ZCmEFp0qi53TRojU/lCZZ2zzvlB4oVTA1ySc2chRFmK1vOU5ofPrL58P3vgXp/q3zzDDs9f0mB6C+TkVH6FJyuD0bc0UF2kX/i/OhJYk6LiV5vx1bN9JqYO29NnTmqLKBmAwpFFkohDwtEbNqFPezVTpT0qfkxsT6EkRdUS6OMCABBa1OBx3zu8u2clWoQQvUEP86UvQHJKm9EnEc2VuTgKeZw== sign_type:CERT terminal_type:API timestamp:20250801095330 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:30.890]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095330","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:30.890]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095330","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:31.307]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095330","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095331\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:31.316]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095330","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:X66+C4i2fCsB2RItv3jsgiLZQeoqr/u3Iz7H0tsWxnvVnuNve3t8GGOaIohK0OW6E2WU1o90GmKH9J7YLtjAh3w1X9m5XH9SUkMoztmIcaTuGcZPXkuNAs8scJlZTcyRaoBurfwlTt/1ue9tCz86yvQZh1byEAaski6nP/MNm/pSOgTzRh4NW+YLlgx9NkQVPqqGsg2WVv6LnFTIF88Uy6XNZCdqQSr+8la/mbvHvM87MrL3xs60m8/1TikGSSwQC0Lqw1ByPC1IrfggC2vJ6fHnr8IMdsfEkvR0dDUD5TiAKmcTbifBI8RIKHWGoGWDv46lfAVYR4g482Sa3uKPVg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:9SObnG0e6+5SXEyLWTfMKVkqQWwWK5lZMu/N5M6GUU4= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:+q6Brw3M0bhsrzbDb+oSRA== service:fosun.sumpay.api.trade.private.agent.pay sign:g5Vqu9TDgQYEJ+qVd/8mBqEIiEC13nhQsB7LSDXYHvQJOx3d5EsfBHsJugPhkiW/iNiTLOLC8VfqFHPB08Am0n8m6FU8HAHISJV5NzkO3PH+BpuItqwHEX4RANIG6SPSRbQI+DWFMmhYlRstdq7OuiIQPTq3xXTtHdK6aswHtUJ1Og/kh2tlQRsGhCz6Dl1RtOYXTOnFHVQnSpZSHxvbcfo8xR2Ry0DTyOtLSf3dIGnSAhz7EW5cDV5AYZPErEt7dcqVPNveGWBFoCiIlYVQVgcxpho41qtTnfQHd2XPBGxLQlCad1IwUdGRJq8d6paNaYKrSJAASHrsYL9P8cwDGQ== sign_type:CERT terminal_type:API timestamp:20250801095331 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:31.516]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095330","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:31.516]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095330","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:32.330]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095330","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095332\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:32.335]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095330","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:OoofafhCSmJAcMOS7rRZ/e500bf7+AXuqJCPTFctSEFZgqRXDL12QC3/HQ8QPMkVEEBPVMTaBaUdTGFbb0O/C9K7qtpL4FWKAPnnOsIapXU7odqVL3gMfUp5SCTd2LpJqIQFteXO4GDkrGpMBoTR055XjtebcmUiisWNPsnU0FOURaFxUXTyTheWQjSAJgawHrJ6I96ouz2Xw9xXwUj/lga0GoC+HIqAhB1+2Nv8wiu5hZFhHhmvQBoYyYwKvik6shuUxzArk+TcbLo3V/TPH1tx6VDLt2yo2iStqZWAA5WObONjGwOiSo113V3dRiinjcgGTKBOpmX1cLgYSRET3w== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:XTpjpJRnO7wFrq3jsRQaa1eyWQn+bGzajOzZQGIZsfY= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:CPgbbfzumS4DssHnLy0FmA== service:fosun.sumpay.api.trade.private.agent.pay sign:VF/8J+maUoI0PWKtzB/HWEWw30md2VkW+7blVaWZ2QcJ3fDx8A8ChrH1RIqQShpobsfNFwlhHrv5VINNohNAcpD22W3xcZtu4vtBhofDCKaVzi9rWzRMyxlsiSV4VnH3n9RsXz0ef2f5Ul953l5d0Si6htUyotoJwbMJdcazxZD+9tn8ajfbip2s0YhehVQCcCcznQzvAtV2Cf/fulxXHbI6yd6BVUeVpLjrxfVNRoycKuqxW0P1WWkgKI9+2n6bjrnOYcbVwK/4QSvvPxA5wQQwEiNnIN9B/lMLHlmdciHCQJwFOvOreclYlmSNMvuSfUnvUEGfDp4Yd+T9XypXiw== sign_type:CERT terminal_type:API timestamp:20250801095332 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:32.503]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095330","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:32.503]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095330","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:40.325]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095340","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095340\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:40.334]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095340","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:oW3y1/8bE/rnwObAiBQXzlHfy+UgxKeMxRc2dB1vfHejK1Cib8EK1yGNaL/xjzwiHiZ1A9tgkewujwgXSepUisQxDEdDh21St3CadTiRu2GSRM56TlO4lgr8T9s2g4FHHSrSnHJwTkH8Xv4h9rP3zbadBDAgY5eW1HZ81HNZX9I2pWxgEqduAOCgRXFD8yVFMgoBrgkc7HAabREIfJI1cksHe71kxQem5TAiW8XfXwckt/RcVdgHxWy/yMJMqLXMIgLoDsKS7tE0TaXlVIT5kKI0QG56cAfb/k18I2AcbHexMJA68ld0eKRqqlTY93KZSE0xji71BnSddnKlTlPkyg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:aRdomP22LbJXcALC7l7xT7/XWDO3BfJUolpIcIUrCEs= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:qjbLRNLWFSgsJNmokU14ng== service:fosun.sumpay.api.trade.private.agent.pay sign:Qr8YXmuFbvFzJBwbCo9kxNaSwYnva68Ooy4iRsOFremoklOb2xsJX71i++0cdpxDndYKi2tE0Yv6q4aS9NRhX56o0P9H3hPNL4E+i9fA/xjedwghiaH7+J8FNV6cs4I5aXrB6zmP1EqBnmbBky+vVlNbGW11PLcdxzAY8loE5dg81cZ0dQ+m5bleXYD+ILulBlHfUrakAmC7EaXt4ALSdApLO9LKtppjlQUqxolnx4D9myfglW7mhco/QM5odveUNBpOU66QRM/OP7V13Qe4R29Xv90i6juCMviAEhWhwXETzxGZZ3I+8WA44fyRORRatHTgXaLUbP9Al5+B0KiT8g== sign_type:CERT terminal_type:API timestamp:20250801095340 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:40.557]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095340","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:40.557]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095340","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:41.008]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095340","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095341\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:41.013]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095340","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:vw5DcTLnw0E0wb1TALEOXTFunahp430mFoBUYWXte6KMVtHGmGrYIwCRejuglFAJTKUz2oV7FlV0S3S1XXgMlFwrzARibgAJE6w44p6hYqXuBmDUnXTHPmn+eieH4e8sphkUWgNjKVboBdIY5Gxt9ZIuQlA4MlOCbNdbBcrJiy7lwlxDUSKi9LIUBy0I7uBFU9tWaeMtF/AEE4UQ7BAuseEVErl1zdpupyf2bwKY/ndaJZY+0l8bKfiULz7FbKjVAAYS9Uug+AYcc+oF7102OZKzgZ5aKLbIk7bt900I3eHkwEv0fg43WIjImgDDPw/DrsYDBXut49ZUhxBl1S8hNA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:VcM6bxSvM+0BbBvmIOOL2WLmeUsMflpTfUW3s6OuquI= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:PYELHnk7E1jWrgKpzQ2vZQ== service:fosun.sumpay.api.trade.private.agent.pay sign:QQXAFRH8+RzHfruteMkH8JLcZPJqbYx8Zj0giRJjRmnPX5JbuvTvJcYEDUucxblK+BmMzw2tLoxKZTXQD9mbLH2/6CUdJYV5oU32uEKgRw1HfKpShJYFBm4pU8aBOQFNQWgwltT02rPYHvXeHZOrgfmRViF7um5nMlSi7AYsgZ8ssZcuxhY9Xa1EdqrEcXm3EASUjj7u44I74liTYCa4YsE3zYy5I89zaY6tlb5vC2Ct3GvrVAl6ygAn0iEBWxkJg0UXjpE/J3686LxPw1wzJuoV5Zf6ZXkIrnNeZTKPFzPSFapyvL/BWGu6gQcOxmesGQeXeYh1cmxbIAbuaZ1UOA== sign_type:CERT terminal_type:API timestamp:20250801095341 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:41.238]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095340","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:41.239]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095340","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:41.674]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095340","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095341\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:41.679]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095340","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:V9MUaW4yZXzGozsSOamnqoCgqTdqeLU/BtoJg0ySM4SbMWetvHLhEm2HRCKAPnOZLr9DnQcWCiIyUZqpQTMwzlqzoc9F85+K4OlmwRYe3BFIPsUwNU0VukWsjtZ6BFbpWLDFCB7oMlJzN9+hi/LNWCxExCUlIBBriRoO9bklibAt+dUrh6HOAD4seiFiulz7WeUWgJto2GKgKusthRiu9jfbSaL5V5j75Tj7ycHTyFNXHa1X9DYz1cdrM9hVjRk0mt9xZIN02SoVotFSyezcQH8vrJYCG26DNs/iLS0H1r5RV/rswsc3IeIAvTmOiVxmzS1llYRGYRiJBICOPKM/Gg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:KlZSNsl8eHl3WeuasvSIy8lE857JICTTyVM6Tewa/PQ= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:zmTg5F8oiLwCk8ymZ2LrqA== service:fosun.sumpay.api.trade.private.agent.pay sign:lWFXDNoiIPWQyVzfbnC+tb4xcfTBMfLdB7HvfjZ8iQBktx7tnd+gAzRKvEMZireF3ZWygsIbQJRpZfnErhAQ7rIFov0x88498teZvq3Ba/if6JV55zvk+8pvu8mJj+p0D+yjaN/9cIjgHGp9ztvLCvkUNloIdhMacUB0wCFakvtsVBAVvM5yOgTPqHRguDiuuBWkrATB+H0yzhiRS4YDQBVcWthrBWNKX7+hW03dtTwYbZf0Ny9nEuSDG84xYuY5MjEHf0yjKeW/i/Pf96OVOGvp4co/xZIZ3RciE75TrzZ2vg4Y5oYUU/1x9cbu6iFamyXPC89sBK2gFD3S1SrT1g== sign_type:CERT terminal_type:API timestamp:20250801095341 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:41.898]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095340","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:41.898]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095340","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:50.592]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095350","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095350\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:50.598]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095350","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:DP4n27sEK1ls4brQY6cdePC3rwBpBDyYsm0QUGHNVdhFSWf4NfkUh2PSdjdKAm7rWIJrAS/YIyhWqpduj0tZSLqegiY+Qe00DMp2S6nzwJlo12ESxYc6ZA/oq5jVWHLdQu5P83/1Jrwe1yksFwIUOY8D49ez9E/66ZDD2+iVPEpRiOogA97QmR0EL+srrcSUgSY0GPAbhBTJEqCu5F9Z+NmLVFOxnsPj8mPkNxsHWphP4PU6wMGM2Z57I1+1X46SQA7EqpHOWEl2YIH6dfBiOP+8aJCKWO4fkGZXyr+Sqs8eNr25RWxPpg6cnpRPwdgQPdSAheTghN39yMx7jAo+qg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:4HkB9SjkuxsCfVQjei4ubBu50sbWRHBrn/TTc7nWfbA= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:N9GwMA9fjfJjPISDjMHbCw== service:fosun.sumpay.api.trade.private.agent.pay sign:YKRFsEK5CsWb5f2O+jeDf+4zZY6OGwOGjrG8O1f/H7xrulXfCatCNDydlhVG4CIsugrYULHo74vLh0T1EwXS1vCeZYUqDhU2NXAj/B6mfSz5KVSPnASkJeUCSZJlsYy8SQJ6DUjUIGu+mhA06220tO7zIj54O6kztteK1hXRDs4PLisojCX7gn+rRZL3ctXEEl/HLkYT/hmQd2h3/uFn+28pFP2HPy8h6XHabUvWyfhaM+8LvZ10JFAn7o4poHqstZjTqZnBES8IXEvfEjiILEGEbIHn/ZIK/ikISM47KiJkrteSmkCYnkrvZIAjHfTxH7rEWIbp2XgymRnnP2t4ng== sign_type:CERT terminal_type:API timestamp:20250801095350 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:51.025]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095350","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:51.025]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095350","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:51.525]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095350","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095351\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:51.529]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095350","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:dY4IZfcuz0ACri8k1V9xkAKNTobJl7XmJ0X4iImddRXOi5y21g6dRhkGa4DMtxYIkbcj2xvfMI48eKnLJOughu5BXf3PSQoJwznO3+MSdaXHV9YCoLaXfijZWOo6xlvr2MHavNwzHesieOHDVdPdSJ3kR/LbgzxBU/CsVB8E7E3OVVXQSFz4QCY+wfXoAe6n3cz8puaBaCMMn3US8kZlN8qX3K/LDdsJmhdm4/fofidzRt42i2TVKRx56YmWX20aVLBK4nefiLFeHSN09pRplHFcCmBsImqX3pFBE++vTXKZvs9o+sAT7DJzxp+LVwZDebJpSPWqPrp+aApskd6GBA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:qUEqWal77wpDzxCuETo2mUr7Zlt+4W3aC3sgM5pPNzU= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:Ax5EI396Lrit41EFfB0Jhw== service:fosun.sumpay.api.trade.private.agent.pay sign:S5NR2JE+kDXZv3l5q5YqCRG7XHIEBTbxlicKZ2TIo4dyeGt62AcTCDKEwzRr/5gYNSs1pt4xX9xZqZCre2IX3e7/S4F6eQIGgLIx83QWVswx0xnrniAvjz/aF/bR1rkiENu53eHahMBoFP2b1ZtCWGiFZXbzU7Lg7D+wW9yR7v4gFvEpvIpnOzZQnPqqe4if98R5PD0B+OywrBm4TQIR/TOjJw9exekwlhM8OOVr/EcEJbf9TfZ8vaYUuKzTx+x6vXbl6ZnMEPgV6WRV3XAtMa6BbWJMQD5pvInue9yjzm16k0Hfc18FTCnYnc+Y5PXdIenECC1jwH/b4mi8Btl/VQ== sign_type:CERT terminal_type:API timestamp:20250801095351 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:51.732]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095350","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:51.732]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095350","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:52.068]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095350","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095352\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:52.073]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095350","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:k3Lx6BNhoMeDgv4AZ3Jx3W849kjLLAbn70YGqxaCPowbHqZol2fM+Uveem6NL8zIHa06mAZ/778G8anWpzI9+mDnkLDM3kxITSXKmrCBYTxlPb1m9akWZOgPUs+OJGkSUMGfx+rTi/n9H6B2L1tdsrZ6MnuhvkJYZvncxmLtVsgaitWU19ZgPVCLiOrgM+K5vU+i5rRPMwL27YiWosRlELZRXXV1bDd9Bx2+1w7elQVVSCYkFUg2I13vfFr4X0UnrdnWO3i4aLbRrxymowROY5blLI48NlGAwk1NNAhUeZ208woX2wWYQGwW7ZNZsY47wjZZ+hfUMWWhCfb+xZ8BVQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:VRlI8QyngBQmHhVREWwFepu2KQJqtsEf5Magni78Pbs= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:bPXAg9a3sepa9GAW2yX0Dg== service:fosun.sumpay.api.trade.private.agent.pay sign:V3sQR4YRwexB8k3GyFdHHveVJbq8Nkj8Y2D4OTdYla5s/kvSX6AJIclogPlu+b/aFNszyIMoHEbrvvHSq4kGuMuCWmgPdRtlWatE6iQJ4QateZ03hPV6wPSad2hgURCbz/9k0+GqYeuNr0c/MnShHjDlCmDCMjDGCAuvl8l7QsLwBf/uokQKP3vUXF/S0iyFtWx54L2W7Wi97VPa/Ps84BQ1KlmxgHpMWCftS39ErPjC/DHygKyJClSAmwPuDi2Z0ixo/Fd13PqIQt0OHyLHnuj1ArHS9xbQa7RfvAeP5eaKjZgT6ikCVlUH46eSnvtO9h/1OQGiY6g7nsR1IY5GTQ== sign_type:CERT terminal_type:API timestamp:20250801095352 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:53:52.271]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095350","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:53:52.271]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095350","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:00.394]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095400","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095400\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:00.399]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095400","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:nJakERp/BIambVvh9hj6f+shJ6+9XSy23v2QA9Wdll1xmNVbde0S//WbLcKI51DcPukTsLN2/n735Z23HasUAVJUji7aFFr328e87WEUK2WMx3RleDbc7lfXhNZVWUmGwOOGiYpfExvdIlKgXj0VmUH0d4zK8Ua/T4CKGofX6OZJt82QlKWvGQO5ftmB0+lm6/JnH25H9fwAujaC0hLgL7nlxy85ZkrxJc/3nPBV3uNiGuJzcjEw9s4HRci4IoJHnGAKXgnnLWI5X16GEQIi0FoaezjkYky1U+jAYD1sdj9SHyKOAvGs/yBVbCP+O8sXOev7+aouPeRhpCZc0g/f/g== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:+32PV/L8UVlgtDc1qzUFCralx5bg2kWc3JS4+/tvccQ= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:8P/OzBw7ruLnjklVKriVvQ== service:fosun.sumpay.api.trade.private.agent.pay sign:G20wdRi4AzTyu06r2UOt9WQx4dg21Q8ROccGnnhOLF15M0yEQNlMvCg8ifIEq7/VJpPMR1ut1B/NpdBMyTV6NE8LzNaY/4HDcrkaiWk5gWw9/E9IP9eAY1A6xmcvSOc3IEuBEH5IRD6E693G2ti+PKCdVSOCD2C2brjvz33jPQ5QsFf7mco08gN2nVEiLslOCwMbopglcVmBrwX1f5/GEHTDplBS0bRN41VAhFvVT+4pd0Jj3Prz7qrXzXaUns8o39PxXxTjVgYm4sJ5L6q0aPCMBNTBbZkXsYUFv8c9qurHlySZFNZ9JgC2467DBdSenEJGD4eQ+WKVOjdeEG38og== sign_type:CERT terminal_type:API timestamp:20250801095400 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:00.588]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095400","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:00.589]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095400","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:01.070]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095400","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095401\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:01.076]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095400","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:D+0M0i5yjEdKoWfGeXcInrExIG7z12ycTK7zIoZiENppD2LKiSD3sTM8ytQUQV4uOEcW+UPrzkTcnZvpMtyMwBlCPMWnKnf5Evt6IJjZC/AbJ1kPjEfW1NrurKPej4gjEmWxEXGeYVO+R37/fAA0Qrrrl47rHkXRthfup4/+dcRHkoCed/jOwHlFv6y6DK5GlCWZiHe7JiFXazL3gieev4G9O7W1MoyuAnfVaTGvszDB95geRJsDIUlngJ73Ey2yUhoBk2GwhG9Q0dRqSnH6eZHzEC9lIhtsGuAs9VvspTxDg84Hqf2Z8p3+bTiQxr81OO8V1mnyMf6hj7zCcUsCWQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:tlz4SBdXv4IzlW4X2CF+mDpKJ0V2Egj9poAUnPr4OiY= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:SrBdG2/JEkMwP536EkabhQ== service:fosun.sumpay.api.trade.private.agent.pay sign:B8jve26UVrsrfMJcEfXCg/gESiauZHXsqX28awHVZuaQ96+V1LjeCO++XUsQcdXtSCVjvF/BCeX4je8K1AgKbKE4xiecHWyWTMRgbXVInFpaYP8fw9fWLARmcbGjrL6FmB3pEMBLh593H/OyfjaGcGQ11xq0D2wFO+r77OyQOqElkt+tlhLle3rYf5Osiayyh4PjMbXDZh+OKStXN8thfncsjbFOmxat+CfA+cz3NUk/fHCjSTKSZvcspCehE3+zNncjpD52Ji4QI/K9Dk1B7UEr2dPaCXfOvIBbRnsUORvDxi5jycoQ/vtCIZY3jl00glXAyEIOdZ6zvpoTLYe4bg== sign_type:CERT terminal_type:API timestamp:20250801095401 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:01.273]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095400","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:01.273]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095400","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:01.927]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095400","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095401\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:01.931]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095400","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:F836rkDNw8Z/TrUWKANzVsWqWpLVmFTRJK4rbRrvvwi6wRGChh1iIcPFR9oqgp6er9kXm56xWgyBVIV2cfL/eTLGlzUsGBSo1nnhwBHbDF/L1dUs7wlfhRqhXCkXrVDHyCqUCYmI0QvKbyuYcU70UBQCJhsnxKuHVwJOpVtcfERE+ycsoDMpzK0o0yPW0nYiMvKIzUcGsrVikMDKRkEqRM1LvwiT2bXMJRhjMGomOdqQBaetJ2lB2oZQpmYauBE7KS1FhNIWB8MJQeOCJ1rx8umiGfWzo0NYjd4ErY3gt+7GMZMNaOTia4vmn3kOCu5NJJ1147MlFlSLg2bTrxpgnA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:BUjEp6eVG1E1CMigKwza5R3p3P44yiybA/JX2ueOPns= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:7WB2jzdjqf/3aKUwTkiJPQ== service:fosun.sumpay.api.trade.private.agent.pay sign:SfDkiDBYNqAtbZ/6FkoGawKmaTFKLo3XujTUjm/9mPOm9P8vIG5JKRflkxWdcV0uOtvPjxmZ7GVOrvByzfKcIflJ62ayVDDHlCV6QGfbjyy/WVV6T1OKoqFDOGYA6Y5gK7XSJYuqpywt6pM/Vhi+of+dKvFn5Nu+bW2095tps6J2ozKGzZD3k/Va6yI/I6VEWqL729dhSkspNyQgJUktRXnHGrTLxQVGZYkExWC9HkZYgri2xqUtS/bDdr2pTvoOOg1iARlfoqb8thGgthgRmausx9CCeuMJH+vXNM6t6CVTu1rpu3f0zY5u7mB6N8sfznz3WCchfDb2vYQyXWF2mA== sign_type:CERT terminal_type:API timestamp:20250801095401 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:02.123]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095400","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:02.124]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095400","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:10.410]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095410","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095410\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:10.415]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095410","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:qSP1fNw2hCwH/VDnWCi7Z7es54s82JiiJkj2xt0Y6aRJm3ISuWkaXboX7xV60kt7V/5VOgQls6kg9fLMlUADxfaBdEm4f84QQi11TEc4hsFXF5wuWJ8ReYoT2Jths/3zE9RpTCrW2SMvUw77fC49qclCj/7X8s+vSe+oaLiOLRli0rpj9bFV7hWM//J6AGnRcmzRyIBTCwelHI9rAc0+36j82NIn1ZXFBVJVsteoVGxvGmYpiEXSEAkB6+cI/CqMUvSEQ6UzhRWwgO/LNPAAPxizgsgYL34L/Lp09wo+vCR26358uNem68fTDY1nsDfRJw4X/wAtVpimeGAN92zivg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:9FhAgN+L9V3a5RHIC/YgO1umQiS0aLqsLpT+1WL4cj8= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:6Qf+ez07NhGM0yt+FOXQng== service:fosun.sumpay.api.trade.private.agent.pay sign:dqK3HEqrpdT0uqZ8YN7sQrKpkuhGqHS/rv3LYhFnZFUl7JeL0GYqouyMq+LF95YGUWOKsKzY1ZcPTL5wnOlpqxBx6bFMaCviLtAQTIULs4sjZc93400y/Bl0Zgh8hAsVA+agbAB/lTrgaSjK54mAamT/PBjWlc+UvL5hLqpoX4gOHp4vKDPxPl63PZTcdA5GIvA2m1vRqBAjITF6aizoRi2vLVEXS+iG5bJKDlts6tfBrpMJr/DPCTmnq6iwj87ESh4MMzbaCxxauoN6mTkXn75pFDy/2rOKzeqpfJl1zJyB6sx9dz1q2IdK509I+ThTuQdE+/8A6Ne8Z5i3k3XeTQ== sign_type:CERT terminal_type:API timestamp:20250801095410 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:10.721]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095410","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:10.721]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095410","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:11.373]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095410","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095411\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:11.379]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095410","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:XvYxPw5zR3SpSmYSj3pjei4uduUAt25+jOlX1ELwhPn+w0lBIgXvkRWV1Ma3tYDRWbQZNvNim1DwiWUwVIFSgk618gKf1mDyT2na2YsS2yzKo4B43sTE3UjsDIBbdDaVsMnM+WDz2BpF5AUE1aURPFilvaEzdKWOw9MICct96/7nadboRnJiDpa12bBbhPJYSpO8cyuKPAQO649nutwIbFVv6wuIbF68jbVjWkwEECxwK8ej5RPux2ZBxZ0A9hGtCKU6sGBspAA/rbww1iQeuWghUimtECHoVxTls50nrquyGVa9S2BXswtp4+EUUZlkeZL95kXNRECr4UN7gLLFyw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:W9TS7nYPlZROeeAXAOmPzlxoEi3jHMgD4yNfUbHhHIo= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:FaHPr/oo1uSKqtbgqvTU0A== service:fosun.sumpay.api.trade.private.agent.pay sign:MM6V54TQg2ons0dPK5EbZpTDkM+cDEl4nCq5b++mMeDC87POc64+MpK2UbyyCU17Cy1L8dSas9fD9/X0icqHDnuTzzhbSPBPOTKtoOjuTPXvUBpQT55CyOHTor3pAs7PZe2X2EUqXnB5TkZmc3muLTesYOzcbiYa68mQ5MoBw6mbAQSb7PURZcwC+uteyZz77/ApkSMJz7pP4oLiVw+MQ2xdZ5+dROCmnyTvfnX2WTPV+qKzqDVcSGAOxfAIGWBcf9QGDLQN4ZizVGUmTWdeXy5ri//bAw6faAuWIEz0rVr2uTPTCRIgi5mbqwPqJhrQTbupxgNydeLXoVwuB8D4GQ== sign_type:CERT terminal_type:API timestamp:20250801095411 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:11.556]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095410","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:11.556]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095410","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:11.930]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095410","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095411\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:11.935]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095410","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:f9/ky86iZcKCmWoJGC1pIIKNatfwecmj2w2JOZ08jo7bO0q0ZjGSQksQBtAIZ7BYCBpxFGgM5ymd+6WuRyiozWf66sP1016F0a95kg2rgQCxQSn6AdWLiNinEo4ttALWTqHur8XMJP4O4oTNlVI1Ta3lKcLGOapsTyCj/9V3lMIpHZ6U2gX0LdF0XUxJM1tc7cYj2P8ebIcFXedq7z7qcv2T1MXunMuZXPEhcBOSJbua74aRdcWz8KjrPLhNa79Z65yAfk5uzSQ4TcVA0xaEtb41sIB3Bza8XqfXnWNsr5WC0v0G2g2YSnuxvouzfZ/W7XcpyIQDWF8/afcj4H0s9Q== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:xMLljnaZDfELR4kNaG2hpsrVMWaqX2+yt1dqUVcI7f0= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:QT4aQ5RhN7ep/MXaWSAKcQ== service:fosun.sumpay.api.trade.private.agent.pay sign:B9zYHoaWE0o2MQCy/KrSRyFYWrJsmYPGteySho0aG3zES7T7rIj0Ntq3KgKVsDLc+jerS+WH6s7G1j5Yracrm613GePUvnzDPv0Z9byjWeCVcIASHYzB2exRXhy7Cqflxd3wU0kGZWQS0qQpLeByRRHDMeQ6fTqBpKPFPcPdFVG9rSWtQdHV6ZDTAgCz4tMJ5wjiWP/lV3tF/xVdC391tyucXMkBG7VRlkH4oXPzGRNAMoj87ZkVcfbclj8uf8ZP9wn454nqW/pVMR1fHVcg7zpJHhWigFlKWE9iIQ1AQonT1ULbSo3hSxHKKgQsrHGzy1HiuiJ4iqRlcwCavES/wQ== sign_type:CERT terminal_type:API timestamp:20250801095411 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:12.105]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095410","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:12.105]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095410","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:20.323]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095420","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095420\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:20.329]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095420","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:tOMMPtBfQcPri2gGn0vEuMFlXyBe1koG+gvU/ZWXUv50GB1psbuhAc3h7fiHd/JPX1hi0WCKuk7uglj93TWR143pSSHzhWNIwcbV7vbg7UNpDE4Xeu0JK47xk8iXBElXE/QtkoKzGcezcLdwcuN9FeMh3jCENy9bfS/7Zq1Tv1PhPhYJ8IVV/7CGmKhKGPUYmQFXgYRcsXcOhJ20cSrfjoqFo3JRAPFacD5aWPNjXH5Rs8LKdp5SSt1mDJBnWhUN55qOz4+pZent4nlyVe4/hnjjoyuhd8HILsFupvUpcVfwUijaB36OylkF/6wtkaJxP3TIVOX08WiLzywnMbhCzA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:STO7sW7M58Yy5Z6LOLUQhs/iW/Z3dkStzksbn/adUwM= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:1skH8UZP36J4XIsNVOi4yg== service:fosun.sumpay.api.trade.private.agent.pay sign:JCEsQC9bGNi8Uv3UtwGDFcDIsFqUr+2UCaIc2YH19bQdjAnW0Aexxm2K3EvyxVaMUv9qiJeeYrlkr7x/tdMgwCTMdojTnD0PLtlrukr1Ej9VuimqtykujTXr6gcA2fwiXRnkMPW3dco8k5kvXaueAQzfXHjHYOZ+bXpsD1FhvFZwOn7KxgaKLQeKCWmWDmzj5+eEv5AnX+yiv1BxQ0LFiF1HXOPAp4qpuUFjP3uSDjqSPsPG4djlD17N5o3nUQCtI62ZvJkYw5t7WtJjiu8y30Lo3Z0DChlvvF9v6Yf7IJXHRn44pOHT1S83C8leVp6IPi9pY6ughRk0IaerCmwFAg== sign_type:CERT terminal_type:API timestamp:20250801095420 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:20.554]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095420","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:20.554]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095420","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:20.854]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095420","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095420\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:20.859]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095420","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:fRP5u5gd9HCzvn5fHkUrUMiMZCziM7AnD76Dk8ozvit5ocZ8v2Uw0YiYo7IQHl9+RMQPmrCjhkNAz0KypWDAa/UEQDnhO5zeY2/bV4vGheS6Fi/SFggYL5UCHhT//ESPyqx6YSgvsvapNYF9hNXPU88ym5CQohhZkIoribaFJk6SdhW4SR+uBeNIP9hMWGc6wsy1Iv58t1fNsP4ut9/zhryzI5zhRtCKoo13AQHlVbsEa+PqIDcRbUDX3hHfHFknrqvjPYqONDHSH1rMm7EVZMxcAjlHvu7dveVf4A6pvbQapLZCtvfR5jO/NyIpTsssrp7JVbSt+y3wIK3TMgbv3w== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:xI2QS2IP9kLSdVNRKu6hvpEb8VCIdY40loB5b8upmFw= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:qvAC4CG+v+bGLK76Lj26tQ== service:fosun.sumpay.api.trade.private.agent.pay sign:bWTSwa0dUbPV6R/sn5Vq0DU7DhGYjahcmukVyY0YxYY0OFqQ1URrJ7bYGnw/3fUaQKLbgZllAAU8jHMKaKkI5DoIUozDhhOY4VUnu9Tj+YgY9ASI+J3finyiWRlYVTFJKlCVVX+cQTgMpb4dz8WfYnNAcy1FFOQiU2lnMqjaE3fZUyvGVxA0oHMi/eLWloiQeSSnK9FfvqQkKW31G6TtyJLeEVCsz83Ud4C3fMiel3OYyBL4DRsWJfQV22dXmWoBFm4MBCez798YL6QR3Zre4Ke3hx+OPjQ/q/URgzvHocMIDlUHFtodOkuINJ33tgRAY8LiRK43eFpgfiJ0VDR6Mg== sign_type:CERT terminal_type:API timestamp:20250801095420 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:21.070]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095420","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:21.070]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095420","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:21.484]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095420","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095421\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:21.489]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095420","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:VpEHat2KLWqmbFCc7KLUsXqk7UGtIxTKX/WTzHgxuh/V2+KQIZvWUmEqXG0h/TAh9v+cOFnL5Co7rf2nIO8S++2weW17/2q3ru1UcglgyICV8EhYQwg7PZQ9H9wmv5nFGw4HfG7siLdYQnr8svmnCKBoW4D1Ue8/42sxHX8mYdaV6WeOhB4x5ctTY3p0pMRhRnbazVLU5Zgb9h2oAfKKUUYNBFt7SouRkPFM/eR7BR85J/lsuLrdz6gHFURxjwriWPt+N66bTCQhoVX54x3zHlumxEBfjO9XJpT2boOffP3DPPrWl2Rn2ocVv8+xqgqnbc2F3dJ5dRAYWA4MfvOseQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:Ok/QunSOg9Pci8NLVLUJP7Z9VKHWfmfQZzFgbc/uORI= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:Kpn8zJhHsutgprhNXS+OpQ== service:fosun.sumpay.api.trade.private.agent.pay sign:ORTDcJJbGiZPghW04I32pNxD+qkBYStTzCUvcJF97OB0qX1h3X/nK4WR8ZpHhvo8QMsodWNeH4XBuEYTdzdsMQUPgYJ31cDVfpkAt0Ja5kt3+qa0ru6XZ6HB/oNrZJiMcPXPvEUsbWGADnhPS5wVY5D3EGd0DKWgK0zQwcerGqnob2hXfcEFRYsjlK3mL5Zs9tePxKdgx7plORiTelwKfwrWR6nk8fC5j1FFTJYmTWUFdf8oku1A8KCxrVFnCL10uWwrDrEe2r96Sd9T5EGG4g8f43t2SRXJbh0M+VlYEXhzMK61O9YK91ViXX2YhoZEYvpInV82WGxiTbFfAiN7yg== sign_type:CERT terminal_type:API timestamp:20250801095421 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:21.655]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095420","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:21.655]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095420","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:30.643]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095430","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095430\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:30.647]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095430","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:T4ilMcmWnyXO+P7sIJrSAlM0gNJOLdwerMsPm2dXktNLQdJeGZlsSOzkfb2Zb9hi8qSDDf6UCcHcSLg6NafCldbp8aeI5sbASIYOXQ9eYA//L56pwxLRlw6f1EnCsDyn5tuXNMdFZ8Y/vHNw9ACvyMg46wj48B0AYuJwgcly25da3ct53AhcwpROgYG4RwJxOZZe1LfzGdjFYWiXn2VxSgVVrezlewLufvshNmISeAwsI8beAwGzL5IG8wJjolNVs7RXC7xVA89NaueoraEDjAn4MZlcRDY29Go0vrJp2xy3TV9Hkf9nkuTZDTalW4EWXuBKm98SOuc6W/IGyWIB0A== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:NwU9VbfX/gwO0DDM6MbjLtyBi7p8UdOiOkbKv92eq6k= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:JM/CMKlov0KieQMooEUYcA== service:fosun.sumpay.api.trade.private.agent.pay sign:LRDilzVC27RtbSYW6uazWkd3JclqmEnV6p4H07azPZ1W7owhtg9TPV0Qlj/EV8N0/BKZi96uAJR+iWtGvxrr510+TPE51VDQx6NTH8ETTj0fYv6wA2GBAENU0HBHMofB+0cCzUfA7lQZN0mC6UkHckSt4I2KcIOlUKJBen2sd65FiL19Q6iuSNRbH/BOllfIAHHx3FMo2VDJ0EBZ5KHw9EluRbHDVy1uNWY4j1pN6euJx/WS5b+Oya3jFafTbb4qQArfx35x/MgdbbhVi0BftAVdp4OrtDZ8Li4/5ONbk9wMWsigIC/pI7YQh/X+4qTXHEIVK9fRsfrjOSTAH8vJBw== sign_type:CERT terminal_type:API timestamp:20250801095430 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:30.835]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095430","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:30.835]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095430","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:31.207]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095430","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095431\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:31.213]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095430","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:5xJIf2v2iUAJGQovSvoOy0jFnPq8JI/MDcZxMFhed0UeB/ZHgaNSXmmBCg0VDwxuvVzVFfp4VrOh8vq7ttpmuwHWDWRa2JNK9MsjseaqmE+3+tNZlJ3sc0Rc/FWiMpGiQYw6S6SG5OZXo5UYS32Bml4Q7kPeUfPTx/lEK2AaXGmpSd5j1O+ZwPlt8ask2nSIqVXbSESZaWWa9V3WHAgABSIXSQNvlpPtSTbb87e2MzNKRT/YLvAqrMsAX+yoiSM6TDvXcfOeUCOCgEQAR/u2KBv1nYLuUIaRt9VRiGclcoks9WYa6cpHRERJA/eT7V9Yu6TO9gZtyYbe/eS8Z/jUdg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:DlP+cPU72rU2ldFoLNDqWNJpoqJl/rA/vpChaDZS1zA= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:uEPG6pipz12ALeIOyC7agQ== service:fosun.sumpay.api.trade.private.agent.pay sign:CKJItLwG3FWJDU30YuyssaN+pboRBWs2kQsMbueMlgAzBgcQZAG053AFwCaZkJVINNQosb8DxC8YWqjr3BOM8Q+8RR0yNgh20ELSv4cSWUljxamhHnNIkhll5GiyCKHjH1bc7Jb2XGtmkp4nQQr8xRWG+8yN62oj7fexloSpLbKHGDmcySUdZtA1F6e6gu3DTMtOIyDQtGY8bykviwEGoVij94d1bxPTotQETnkXotYFZMNhZ37/1aTbw/hSTOtAVlAhMFBBjN4/QykIOJlnyuuL6c539TrkRkva/IMzKNtU+PVGBOlhUpzpbQWLOVl7Z2v1x6WJajxnpZticMMUkw== sign_type:CERT terminal_type:API timestamp:20250801095431 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:31.389]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095430","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:31.389]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095430","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:31.818]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095430","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095431\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:31.823]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095430","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:VrqLadudfIG9t5sekZ5IVUgtnA7S1TWNdieBJHx80S41fm1gQcJVx389iN95fBWQKxpBlmlzngJAPoRCJcGQzBic5bpa3xBMj+FL3UBuYaImL6PFiEpGTptiQpbZrlUDyF0YUYNbSxUo0ImhKJA/NHY7Y/6WZVsrFEJCvaHBGvEDH+kXndyhTTKdBq7zXWlLcXdaE3K3X+Hl1/VCAAwFv0JGDS9Fm2ZGQdytYaH+njV2WnZphj62bt+Vgmdqjj64HTcxvDIfGdlXeLSddomb46e1c8adQLGOHjeeHVDNx/lPFuTgmIeX3I8EtUlgbdXUFcVg2E0Qu+3eDsagVxyDtA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:k4yQJjo0N+6BWbyMQ4qBCkHKjgB+Eule/7YyhTGF+xg= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:n7qDDKEusegjB4o79FwJmg== service:fosun.sumpay.api.trade.private.agent.pay sign:GRwNh+/UDJTVDwDE2UHU/sEethiL2HVZEjTbpLxiY5/NBjeoOTmbmhpWqeUPnsIIqbOUr0CeLlqsaxrYhxv2gtj5sw0sYvP2TRLevnkjdfjRniIWe7aRbuCRP9dIJfYOYx79aS8k1dvB3ARpTyp066SvfGm9LE0LkEUiOJ7mq6AtMPTIhU7CSkQ6S0EnETzgE1C0iauLVO+nTKt50v4s4wLMo6SYhHLRGvDmg8LE+eSwL8ZEKJWQNz8nWsJZtswtxpTDqOjpjqKUuCQM0wAlr+65kLy1Gi9gYU1KFW5msKYsNbGi7ep3lsAZXGjc0efE25evnMSHBsS6iWj6iw5Tkg== sign_type:CERT terminal_type:API timestamp:20250801095431 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:32.046]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095430","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:32.046]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095430","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:40.655]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095440","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095440\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:40.662]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095440","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:IFZQ8dx03rKIY964epymqVZ6/h0moEE5UAemTMJKV7ighlzTo0FogtX9ICjUNyRhEdtwTGN08cUMxq3uA2zA+XzXYSnzie5qNcdQnCYyOHN3k1sRyLk8+MnRckl5HXJTws6i0wPbTEUaM7sevsQSwHABxLU+Kq4NxSWcqIt3m7G11r5S+IC1vdkR7oY2i4rrA+tKyiSECBJNDQxSHwNjgUhTDTPA143SqW135dk0Um515O2hkqIkm1MeFIXNnkHNo/Eus8qHc2zAgfK/U5Y55YR+toS0NTRwU+2aXlS0zIOX4WpP8WzffxaIq2Oh6/fyHXTS2ZIlEHT3tgneJnNo2Q== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:sogclPScGBCeJ4LIId+Ihb59uHMDaffemznVOLmSs10= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:Y/7igkAtbktaRQagH/N0Yw== service:fosun.sumpay.api.trade.private.agent.pay sign:bRGKK0hLAMS21c7m+M0xT5qfwGsB5j8WYM/oIlDKswasla6ZmWSEFen6jW+FGN2kK+7Ncrzjp4ggFSC251GpXykK7fmNPSw7oiQY0K6xGaN2MwgzjDEkBPBiasW2HTaCSUcbLWTgG+nv14LVkGkumy14TtY7Bs771J4VBN7gCTxPTQZSy8wwKwMyFT6z4s/j+iP/0bbGKP2FngUYgMMSVqErUc7gV5P9Xv/xvKnjNqtkV3DYDUwIfKTkdR60g2eFKclY3VBWNayP4Gt9kSDYN+hsfe9lUxRty19LnSNxKfMsp5sAuJzVxxgF+eWRLDGnB7HI+Dj4mffcoSacAX7Vcg== sign_type:CERT terminal_type:API timestamp:20250801095440 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:41.028]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095440","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:41.028]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095440","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:42.024]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095440","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095442\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:42.032]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095440","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:Gqg1aGEEQee/PCwuU+4jGJDsgfmKMc0O3LS+ksLP2c+q6loeeFRQS7mg712wEfpCxYyd1XIGhX4g2h1E3/M/xvCKqVnv0oUuQoiOtUjxM6ej/6SuTO64rKhc2QKqxlNpX6wCyXISwYJdmLP5a2wDb27tdTLdpRYxj/rj+lqCULy619c8+mdq7MYG2pRR12geMm1nccUki72ZJpxk57ns6MZs+ltKLLeFX1BbUwsoJCn5qvnAncCLkhM0np30usQEJm640ElDPokgFT6Q8XnjVBST+F2j5rVJ0x0kvMV9gwXXk4rRqnUajUWNZHgNeC06uSmk/h0ehds/M7tjb/QZkg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:XUZjo/QhKNdPGNEeqTYxG3uSXoifoMNSQqxxSgcAwBM= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:Vit3ZATEkTidQBrChLgSGg== service:fosun.sumpay.api.trade.private.agent.pay sign:O3O+XIM9V/FO7f+DMN3daN5unHT6It/MmHami1r86iB2Fi2Uv42Uc5/fTI20DenKeXnSA6l56Or/vEdPiC625yY4va0g/pEi5yXx73ekLIVLCy7ydftd4OkQwxyk91+N91JGg3qeGkomgNjmoSQcBCdwxdu4WL3Mr3J7SORaycdHWNXjCtKxx//KPwxurc6HKUpAxyCcygNZV/U/xdW69f7inUGFN16MJcsJeofa2WpA8lnuaekb3czR7i8S/C9nW2zE4WR5x527FBAoLG06E0UtDUzzPyajDGI/CYtdK7nKJIyE2s9YPg2sHHED8Ypzxuxx1V1n9KTaz7zvkD8YyQ== sign_type:CERT terminal_type:API timestamp:20250801095442 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:42.216]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095440","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:42.216]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095440","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:42.782]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095440","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095442\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:42.789]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095440","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:ZVyjg+zDMfKJdz+5sJwiGDAhObbUcNRH3MxQionEazGD5feJ5L53r4eWv8pH3u8qjDSvF3nWS16Fyf10SMkpqBqZNpCex0lUK8nWKtDPBoq77IlJsJfEyYcI2mPuvx2Uj9YD3QHzFGOmLmquibLWf3BuIttx7JJGA7ehtcb0hZXmuzQ2sc3wgS/Rd/XlAQ+qIbUXxXVkH0LmC+ilOgDjlNYAleL9E4vq3vy3TijAR7HrQHul7VlkeaVsLudxJ3yAm2Juyzc1nhdpsF8Ga+0sFeWAmuKf6VotlT/LclIhgjnEQhrIkttxdPJqreJLu04+Ihmr1Cr2t3CPyTob4gCkOw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:jiQIJFlk6tuLuXU2kAkeNrYAhjiM7LNssKSxjqa75bg= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:SBdxBKvY/6KoSWUhkojkXw== service:fosun.sumpay.api.trade.private.agent.pay sign:L2StaAHC7uxR8i/dPrdUWDj+3n3Vqj5PDr9NtfL6Qzl8ygV8SzbnTlY/k3LboKqrc7wH/C8pLK6Wy/WlzN/0BaiAodUs+RcRvqyuAwIzTmP5cu0nUgr42lPOvZXK8//40/HgORXUE4xZ4uNkwklggm4KGzeg/DbT2x6NxXgo/c7IOcbw4WIZYI4cpQcYnajYBqr9RmhgyGFzcpjNGQIMsaLD5RZeI2OU2Ix0ljPROGLsmzRoHNqjKvCMlRqcbb/8GCSt2IkAsC5jQB6a74vqL+FregZPB4nXTgpk+ggc9SyPPFubjZXvmnNve+5rCb+PRRbb3hNvJ4zdThdExZ+HEw== sign_type:CERT terminal_type:API timestamp:20250801095442 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:43.252]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095440","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:43.252]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095440","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:50.274]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095450","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095450\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:50.278]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095450","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:I1MlD6tCdf/1OwY8LO1oAIvOqUCQIHP1PZStojAAsbX8zh+qBPvIfxcQn+4lSgSQoUD+mPT8V1e3bgjVecfT1JmcwlceGLr93pIxUpDFvftiV0CVYoLCgUt8ValQ7SfVZCr90vSX/J2QtG6XhVE9zD4Zy6/4NFYrd007k0Jr0bkFh69CqM0W3PbQiV73cz2tSN7Nldno0ihX7XqJqVdtvCgc+1ttBjgu+TESpEWH8ANL3vOOnToB9E4/nEugUhqti3qE98cPRulqp1K9O3dXm45jQkToUBHKxKabtw0u9Q4I0ghM5bK1CgIBns5Alr5oegZY9f0QVGgpqfQfGEh2xQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:Guod+AG67bt1xnO/9tGm3ueLr5r6QykLC9UialpYWAA= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:LMz2M6E29gmk+FnDxOubUw== service:fosun.sumpay.api.trade.private.agent.pay sign:ObHTGUpFdsyf6OpbhaECur7Yhytt/In6gXeBTByokNBR9Gm0A++uZrYRBUmqFNLixdLmhfswN23JNICcNgPnOcvLkKLnGhNcy7X0FavY7C34KUqaKyUu/GQnD7iVKI2TL5cgqTBb/sFCaq0d9Cik0WIFA6v3cR1IRy+w5IWC/O4lDaf90P9T0sscMOLJPyZswNSmSA7gD8RFVYNRDlUw6fJOgicJbSekTzrEEQjVRpqCiEn7VXBs+z7F/B7C2f4SpU//a5qouuNSRRIQUDavQemuvlAIsz0gkkR16+EbdOnVaXyQh9EIFDAnBKB+2LCmdFXKph0pPhuqnqzjS6Qdpg== sign_type:CERT terminal_type:API timestamp:20250801095450 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:50.986]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095450","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:50.986]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095450","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:51.575]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095450","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095451\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:51.580]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095450","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:M3J85CFvcxBtdWWoItKmj+Ng1pxF8h4LOG9GSL6MPRUS7VtWO30EApWxZNWbaA/8Yi4Sdr4H9zahXxg22lourJ0D99EyEVz9l7YrNKVMPJflEQQ0FFwTWZaO0OGDOFlQal1FfZ2clev9osSCQDmBknAw8s3ynG8htVq4EzrFvmlCpmQLIx2rCthe8FmW1ZLL0muGNXWgCblxAkMrCjGDeHbLfDkYi1t92P6kOf1pwKi1JA1R21FLZ7/xCh5/cvjdRjt6AHV94K/0uBVyJslRdMamODj7KUD1Q+14p+65TXbs/HdarGtj+sw3KFSmynz5JOe4FVSh2qx7e1D4h+jNng== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:wgp03A6xlNjrpL3WIeVt1DY+vDy38v7d4js4aU7ar28= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:eAIi7CG2suRQETbQGZK/Yg== service:fosun.sumpay.api.trade.private.agent.pay sign:KQcvg1ioNTE4XOVeZo7U76zFBOVBQvs5yWYQR/ML6NFjXMo0NJySsMQAwIUaRc2StPbJQIWBSIs8+h0a23+ScIFblUHEU87LDCLuXeWE/R+dxsxiZPyMIwxEjLDR+IJe6mbydg8q1uDAYomrz1Jqd/PBxMfuUBPIDNbttR/MYZtAEgBx00+MSHvzNoRI9i0TxSyJFjzam0NMQyeMrrGN9mdrcDHSIJ6HsP1avL+n6/OM9YXYmH9Cs/KmTqe8nBrOh0WUPv6RQsrex4uTPH8kXnnWTt/z+QWoZvvA5BpP2bKK3rnvlc1krUalOhRHUFKwWRHnzdqQFVRqthZCDu/z8w== sign_type:CERT terminal_type:API timestamp:20250801095451 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:52.261]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095450","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:52.261]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095450","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:52.790]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095450","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095452\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:52.795]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095450","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:A/BMgwyH7IDd/GrjUgS34REuIQ+Apr5iYcBWKZhhMAWc7DNfBbMUkVlophtAexQS3+B9rzVe1B7vxwLVXqECT+PxrmyD9lWcRpjRN7W+8RUz7zFAfQzFMoLAv6NLGXkHEw7zB+FIdCpCXkW3dyrgmK1EZHWffiDTaoSGfmRU/KiV/EYT03uF7036JALCayzYdqYDWOownmNiU7KGLgYDLsYA88CNSzMQHSvUIJiExDr6ukCRPA1lXZVtlfKRMXkUQaopGYjNG/bcE873fAx2xdwE4EMW9Jek8sJ/3rT7Uq7F+jmiZ8YES5xEP2wNe7N5iX4ggUSe36sRtWCXZnNvnQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:11T82VMIVmrcnzmov9QSg0466OO1CGobPkVgxjuv1Bc= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:t3siU7UGgKl8eXMsA45wKg== service:fosun.sumpay.api.trade.private.agent.pay sign:MZQnuc2esR5lYdpFAm5py8BM66CSDWsQQgOrJzmbKJhAYJHszHBQq1rR46E9dNyPQ2RbLoFLz4gjiWbFimMgV+uGu0jfnkofJI8zD0jOyTsoHSq+sJteIea0NqDNBXXyku3GBcbfNuvanIDStJAdDkCIy6eB8yGOLwfie9l017Un75oEJyi85mqICMaTHgyREwt04ERUwrItmyxpVzV/e55U/6gIb9rZJi22Qi2GofMq/UidksCOPjZeK0nm8E9lRpdSu5Cdtsi36S95RJvDxBOKIkFD8fuhuG/lS4BojH1OsHTZcwIr6eev8ksXhK6ehrnhAoa+PRT+rSa5jkwiNw== sign_type:CERT terminal_type:API timestamp:20250801095452 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:54:53.037]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095450","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:54:53.037]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095450","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:01.892]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095500","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095501\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:01.900]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095500","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:MdzlInFbeE8cKCHDq7IaaxDF+yiKdbn5gHTVk+LEpMaNz3ch5+NoCyVYObjmWg7KJY4f1v3BbW9QvuBsjdd9616xI7ABO79G153PDy9EoJU+d2EP4NhOQtg4H17CH9zwHe5lTM9kHj8yDH/1AeJnmfKTqr8QJIPph4IeoVe1ENW2B1dfQaHIkpCHhlVDR1PcOXdKtTICWKov2xQyQRQ29hReCk9skJAG01nvE/rscyVWnbDDBVLDY5RJvyZh6kZOwbAm1XQxHJf5gudLdD39qc5IsxtGiQ1bXKmnc+t3tPLJ9KeEOk30rD1Z1m876nUpdB+JDJGPlyGDN+yvsiutMQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:FEdPLmvQT8sWrzOKaKMfTaIjcyOzAhqKM4jzXmt/26g= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:i6r4dOLovyo8fLoybybt6w== service:fosun.sumpay.api.trade.private.agent.pay sign:UdCn0EGJdslCltoFbQyPF5zDOGBTeY73JdLmRxAnrFfzr+uhZXMWfUBa4MC+yNMapHbl4aEsO0Ye2DtB4BMN7QZCfjZ71WLwyJCkzV1s6HO7C/htN2farM1SC94L2yoaaw1rk+WPKXzFFGy+1FwQk3F+r7C9TVA2gVPamLTa/ZUVM2KeNojmSjwZ7nsy0cgoyO5C6M88x+HOV1gB5QlDPMyxieZiV3TmCddA9qV81MK3KIY+g3w5elMqYHRcmAzfOxiFNlgasZ5H8xWmTeaYU8aMTnsz4E0RT2zIUZaPTeQkOh0EXHEwRwzoyOfLqj+rY0YOQxpPUI2Mi1geJbX/QQ== sign_type:CERT terminal_type:API timestamp:20250801095501 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:03.341]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095500","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:03.341]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095500","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:03.812]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095500","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095503\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:03.822]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095500","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:QEAKznN14Ls9lMVLL7jcxEHj85qseeImXOzZglszFVmsavkQ/qEaD1PFXJeSTyNKEq/dC3fvf2mOTMeL19NTJcRqsjEzGmz1Ew0M8O15+/Xl6be3oCSHt3aIgYqcVgIxnv7loAKFwhgUEUh89jLzXC+1l4i5JxYIEmWhXuZ/uw8lbj2Q+nokujBPb1qt+Un+KpWCTkuFoxLQv0va1X3L0tHbj0HzUBp1EolEJE8JVnidiAFOliY1MUcYdLxwQbq3ZeAsk0OiKua/dMSkYD1tbV3FO43MbTuOJrrBI43R61KMD3kZDSrxTXzZHFK+SEH8URlp+1zx1yXU1W8iHtttxg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:6Q4CV67yCv8QLhrz9WU5CnStTSGyZk94sUeIIFMJDcE= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:d6nqpr5GFuuGJgdn9t8ztg== service:fosun.sumpay.api.trade.private.agent.pay sign:KFiKU9WVDR1TSwzL05S31YWcZKbAqthS/rDNMtlqCa/WsCFldAJ/iFzDYnSpVrAtA/0zEprwcY1xk84Q1oUlIyDLuiM4JK1lH0XVlVxFLtNMLW0TQVs+fwgJkIKtrLKy6tiKJi2+Ptp/PO73k2ogqoa3e2GfaUdGcbLMb5dtJsfTl+h9PVFunnuJ5ZtiSQuP4nfBjsI+PRbvNwNsrydRDseLW3VA7U9RqgGlck4YzwDuyLXcFlvds+BCCgvehD1ZY6H1/YGkwkI0k/rkpGrQDVAy4BlSc3Iy2vsLYNDfkBRK53CLm7mZPT9nd55jHMydkJO1KKnZzvD7Ic85blO0VQ== sign_type:CERT terminal_type:API timestamp:20250801095503 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:04.143]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095500","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:04.143]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095500","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:05.150]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095500","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095505\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:05.156]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095500","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:wBbd3Lw3Fcf4yNBSMgqUjcNkw08CSjbaXN4p4QavVDkNpPHynYFWt6aYWoMxqw9I+tMrYWbCVZIwI98ctaVYhvaR7DWN/sJBLRKp0Jr0b1Ey/dMgj81wEDIzdmVAqfSYr35UtZxStq3hZEGBiCj8vyw3FHjey7hS0ulR/XQsg06iQHyF0S/ht7mXVU0XIPaQR2E8Y2BFIoSCEGgxpEg/7kyLWckXYAUs+3CFDWI/PU9c25D6Gv8ZKq2v5tYbXNKQ5o5XKPH6Yc8zAtI9jSfLBjEHowNrbHnkcCrir13pOQ2L1fyBSjlDWbzxoFw+//TUiL69+/AED2K8NJ+B7ZPkVg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:DfLXOQJjFMQSh84hmfSvYqUMct/xn0R1YG6mCcw14AU= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:bx1SFEp2H8WY/N37PO5wKQ== service:fosun.sumpay.api.trade.private.agent.pay sign:W8sQFXvWjGT5XtoDpWj3Okj7DmMBfYAhIOt//nskpqWgPGcBMVFlQLyGA863yz6AFO2cOpO+q8jx9qI7Nx4rlHE+JJJ3oodODDhMk9Lpk9ABv5g17w/zPf5y8IPvFU2X52y3xOqQxojC+ItYef2Vmu3EKJJRZrU1xCWJRX1nv1nLOj4QmIiqHMjAtyHDW7o5XmK/zxpenwcUX128KIIpRAaGN8kbxTWTayhwowDq8TWx3I465UzTP4aHOuNST2FeB4a6Iv0slMSFmgarOQ3xrzD7yOlWyBvabn8T1PBO76JWj2NBJkqz6h2e3jdNouCWkqLh8dABI2P56wCIsK8tog== sign_type:CERT terminal_type:API timestamp:20250801095505 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:05.623]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095500","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:05.623]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095500","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:10.384]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095510","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095510\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:10.388]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095510","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:6J6mTdmUMH75iT6jGasLsTkXju3MP+2I3smegCMkfKLssaVsnv43lhoB2HaJ33he1w0tkpEojEx9f5D5sgLb2raw7IxWoiD/mzfjdYVIGXqIWpuPo9aGWhjWzzzxi0azUHnCYvQTklQ1pJ+g3T7ofRfBIBRiTUIRQdR2xj7ftNWp5jm1e2bKgwhSx35dvmm9L27+y/n3fsEGT/KbS6tJaytuST0+uazPm4WdiEsgkZ/o29/4G4s8tqgVlofR5dgDv/OfzofAgJ5nULUaAXfUdE9l7gBnfzHnuRNL3jldJiWGfP3c/l/mVJvQ6PuVKSIypAMD3OzUdFAx2Pa/b3JVDw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:AvhIMz7b1Aq3ZqF90b0nyzovHK39SRz67ERGKHzT8b4= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:Dw3G8wqYbqmKLZV4I8rIlQ== service:fosun.sumpay.api.trade.private.agent.pay sign:Vnvf+8FNkG2rJaMygUBYfT0ZNb3UDhsA1sukXw1UnaO6iYurl+IUtXe+NKGsy5hRuCI9irDnU7g1EpZTIEBUo8NqAjJeOfxOE9sktlfW2cE0NLon8oPB+7pURSWsYmPvBDqOHIYNHJHXYEg+B2N/jEhTrhcffQzv0T9XKDsH14FCsnaW3Vx7ZnUUfD64aAX/1FvuyyoVH4aFjPRsviXQItCF22n95K8F2tjYmjtt6hOE05zBrBz8t/gXz6MVZVu0X/rCOEQxaRK4Q2TwBaitLdxk6A+C3Ik7LpZnMya5X8sPvjytUjngiXyU5twSHNaSj3tnXgdPGRhK8Gexu6uPLw== sign_type:CERT terminal_type:API timestamp:20250801095510 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:10.585]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095510","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:10.585]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095510","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:11.305]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095510","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095511\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:11.318]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095510","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:kO730FFLGf7ex6qS5wYubbQomnXZ0CVCdGkkBLszLEgomV8vrkOnFj16Z7i4+2C1L49RNuL6bXNS8AFO/bErp6FIqBEdHXUy3cguJomh5CNynZwDpK6Or+LdKkt3e+6ZnXIcEgJfy7qOSWU4SLDYOaaEuyH/B/cGMNEplnE7lus1BUGlyBMtcWsSEWgmwzERqXjO2MYM2uf/BBj8CqwgfDuA2dMhvsQzehkKYTXcHnCjLsJ00QAdrmKv2cIViHxH0ydynmAaIzVor98cjFuVlQeTq+8FIJ2dgwSuxpdvt+2YJmC1PX136va5vAWIViqHCnGS/Al7WVc2z5Ul8Hyutw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:4K96YA8NiO6ftacfMnP/qtbrkjRYKI0UCUXqQeHR7aA= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:6qqoQvNQuDAHPBws+YYRTg== service:fosun.sumpay.api.trade.private.agent.pay sign:gaBYdDAEp7WwBX62OHUKoidlKfBNhr2/kqW0AYcK121z3uxaN8G3bZf5fEofhuLdscC7A0iiZpw8/u5bhrYoV99NdypY9TOahwZ8Nj2G7sWFtpd9ctDyl7KZxtiWpWHf4C/CwOwjBSnrXyhVOm54urb6GKHIjShEwAprYEUy9iswxykTwOo6etyL5UzVOC390nLYQHkxQdPKgoIy26dxBsou9PUYvdTaD+6/Zep+hNKtLiMqqB1TkMHpoJLh2v/KRpV21Pz6gvU7kwz73F9efy0pUS/cVhXxeDlNSGAi5m16LhShY463szeJ4YH6jLvahBeqqZE9SAFTSQbAiU8s5g== sign_type:CERT terminal_type:API timestamp:20250801095511 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:11.493]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095510","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:11.494]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095510","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:11.973]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801095510","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801095511\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:11.982]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801095510","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:MtuQQcMfJi3MLpCbnA/WDLcjp0Zxg7nN7HSpr4DEJS9IaBUW0cCYg5B4UUOlJi7Q/mXBVSVYQFaRdjoKOb4yMDxF0f6lirr7nQutAJFqYu+69LC8FhknpJ1MmYijtF8wi5hkYeJdMrI4StahThREOgeF4BjdCCf0ippHSAtPyTuoUvUJgbYkuffZUn0HJ4XvLUImeL2d/P8LDbIv653cBmLMx2k4+xvjYaze3u/IUtosXFqOmgZ3gdwb2SV89JOhFwuyH8t++JSiMQKGPUq9Rf3kMJtf1Yx4pX9ZVMxbMf2TpELmB0itCpvhdn2gq61NaadVlALCFzO/0ozfY3NNXQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:CNeo9jdNZ80QiScOJvEHG1ZtE7KZuYA+h4ZEpeJiMz8= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:4F8kb7+gaeK0+oYbjXu0hg== service:fosun.sumpay.api.trade.private.agent.pay sign:L+rSQDiv85eKYsZbHWP4JbQYO3IM2Gges2gr2GFEeawaNmxgNz7Ef73JNOa9Kby14W2TVl/yhFCkgvJeUFLr6Eo2cd4k3q4ThQa8IVawo6z6peZaFi5SGE7S5KvcdhLCZcRfLBmshSZ8UlOt4ydrzv1SOT232uRcTxWK+NP4was+7eVPWNAnWXdc8vJqjTggRnLhU91I6BEuJQWvX6tSvhYy4qcWmluPr/xJciWn1gC9f94RAFZlWs4xFjE22FUd1Gld5iFz2eP5ubqMRNBf/t7RmTfsf8dXup/ouD7RpYD2tvneY+GguYmyEey7i4faViwhmmZU3BFL0NRM0kD/qg== sign_type:CERT terminal_type:API timestamp:20250801095511 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 09:55:12.231]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801095510","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 09:55:12.232]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801095510","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 10:06:00.252]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801100600","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801100600\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 10:06:00.258]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801100600","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:2JS+hQPXmsCzO0dl9e0t4aQEymOL2JmPUlCacVL2exlkJLknf6DWeDZubFNVUSnAiB6AksGlE6i61ikZdq8JPK16ZUBkBhf1vb9Z9KsAJnpVVGrLJ7TotmLy+Thsja1+oCKcog5jsW6GBOlMRVsBoRhJgC5BrCYS0gevbFQFuJ/LwEzr3pf29sfkvp0pmK+404uACecQAyRv1vYAtDtasz9WmehX3bXMDk3rv/m7irt6EZF9yqdUn968WaGORFmkO74YHYfZCakw2DeCCzW0TfyWDgerKf4dqMlRJcafB6wijXNTxJ3jw9lW7zPJNDOgOqAQbNudZuSLssibxlUEEw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:s5lEktNn6GJqzifPUW1DH6WZRPF9t8WSPa758//y/ag= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:bf8MW+nbcHOqabJYSIXHBw== service:fosun.sumpay.api.trade.private.agent.pay sign:izZ8NGosDkotxUEjLPgULdeHTnxndmAwDYU/fAT/Vloh/AdoNb1xHL7HbOzzl29uDjEV4+f0oqaduWerbQq3waV25rTDQaJT1q7Yoin79Z4wCFRoay4O5oZohWECH/seQRoVr4CJskrFwJYfDMCXvBC5Y2trkG9b+dG/W2YuVJu1H6zeaz4V/sKV87tKGc9fD5KfrU+Tlgc5HMDChD+uvy3yRNK2BASKDzzQGgG1Vk3IpGDmwzjVX84xpayFsd38qVyeqoIgrUSVD0XKravX0xmjj6Dc7H8eipQGSq4CBJS2wDGoxSkg8ouGKWSlpbXb56ZyPAbTwPznj/UrzYYL2w== sign_type:CERT terminal_type:API timestamp:20250801100600 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 10:06:00.456]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801100600","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 10:06:00.456]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801100600","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 10:06:00.683]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801100600","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801100600\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 10:06:00.687]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801100600","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:f0R1hOl8Dad3SOkbKXQRF9D4Ia73bcISC/QG07VcCkn06BNkaLOVG7a5Ck6NJ3NzEx0RAFajEIpTMc1bl+tuuWJHyudtDAKRD/Txkf3vSDmZSagzU2ldGsHPbtscviZjqdMIAhdPsT9yQyQRgzcHRZbiJAROQHmX5TXi61c/sdv6LwbZqCvRgMhHYo/jntxeMvV6/QOnS8VHZYfOjKjTjkLLVOUqVxFsuBONkf8ZsNhDouJBxf2x8G5bBr+0Am5fWnyDGRpFdO51H60QZdNN7ovbWeP96aOPrp17lm4Az+9zmOToBgnGHfucUOrEBFDEoPvmM5SbnPk1+tTppd+/YQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:VEEQ984ALVkZSMaK8etwtceWB7TDnvOIhn8PzU53cqw= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:KVMnlXMMUYvk+Ey6xN/lEA== service:fosun.sumpay.api.trade.private.agent.pay sign:etX68Y79y+18J5z3VymG9j3AaP/mLOvyseTCvMY1Y7b7p/YdCG2P/1kTYJk4F4dEdbOZq/3WQ5b7MA5lfUdjjlNGGVitdrTbj4qU1IwSQGvB5xwtJzcvTGxtv5lrownMRhYFXmC1bH+VXdM6JI3p1bbA8jGEliHVtcUF0TFZ0Zfs8WDkTgqVJOG5BZciQBs30f9rpHM4Nvs+mpp9cMOx6zmKXvwrExO6z337SMbOYlgo2x8fH0awHsYPk9gPd92r6K0Kv3AK/jASI6SuM6nCcwSTZ7gZNYyjYXUGy1FfoZmDBG6dbUwCDFg5HBoyRfRp7ADW/tCF+CSsBrLCKgOysg== sign_type:CERT terminal_type:API timestamp:20250801100600 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 10:06:00.831]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801100600","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 10:06:00.831]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801100600","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-08-01 10:06:01.041]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250801100600","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250801100601\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-08-01 10:06:01.048]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250801100600","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:0Kb+PnW3yxXU8cuLC7b9neBF2Q5Zq5XXPOwh9AqEM+WxpK0PN2/qIuWd6cxO9omrkusZMnY59srXxLob26z5xNWmG+5+tPK1EFI6iF1QHcASMt0xiknt5ZBeZLIuE7JZvf6maoU5B8w/BB9w7QdqrLEsNsYh3X6n1R75ybzXIoBpT5uoNMuYwtQhUqKbzF1gKm6fbV9zcabTAE+Q/78gfKHiNaGvqDJcRoYzwEDoNM0Da+aGb2cG7oO45MRJSQr3QAknKGrBTuSIFkjchVWse/UUfgNG72S4X2WONd6KTOeWKgM/ZC94HfctKs58dA+XoaLo4UC7Azgi+DLYGz33dg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:/fEYM5C7lQ9GGfdglCpY4mVP6F8cHQ+0HNa0/Svsm5c= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:6C97pL5LCGTxX5dtcxWtqg== service:fosun.sumpay.api.trade.private.agent.pay sign:TwGXvvhKw4jJv7JEICRhAZkw6l1LSSrZWygmqZ3PQTv2kvDTJD8pJUrkCcQw4BWJML7tsn6JV13Is5GDYtvG59ovG2+wwO3rcgQRnkqMx6ct6CinLVJGH5B+Z2lQg5J8IsToofwBUHs6rp1lIqoL8pTlTVwRc/hnx5NGJT4uSgu7dNkAIlvco4LZeVWHmvWAB+ADvu3iItoN83uDthmvD/h0Re1DJioQV5FTVb21m8Zju2alKhAEx8fcZrE2j5o/IniY6yt6M1J9+xdPqtqYnS28phstURUSmP5VBSro/zKQsG5eVdvl3cLYCuu97ZuRCgdk0231ulUgFzAnHh6eVA== sign_type:CERT terminal_type:API timestamp:20250801100601 version:1.0]"}
{"level":"dev.info","ts":"[2025-08-01 10:06:01.206]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250801100600","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-08-01 10:06:01.206]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250801100600","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
