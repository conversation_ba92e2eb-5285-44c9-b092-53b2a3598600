package order

import (
	"fincore/route/middleware"
	"fincore/utils/convert"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"
	"fmt"
	"reflect"

	"github.com/gin-gonic/gin"
)

type Manager struct{}

func init() {
	controller := Manager{}
	gf.Register(&controller, reflect.TypeOf(controller).PkgPath())
}

func (c *Manager) ListOrders(ctx *gin.Context) {

	// 2. 参数校验
	schema := GetOrderListSchema()
	validator := jsonschema.NewValidator(schema)

	// 从POST请求体获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 4. 业务逻辑处理
	service := NewOrderService(ctx)
	result, err := service.GetOrderList(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取订单列表失败", err.Error())
		return
	}

	// 5. 返回结果
	results.Success(ctx, "获取订单列表成功", result, nil)
}

// GetOrderStatus 获取订单状态
func (c *Manager) GetOrderStatus(ctx *gin.Context) {
	// 1. 获取路径参数
	orderNo := ctx.Query("orderNo")
	if orderNo == "" {
		results.Failed(ctx, "订单编号不能为空", nil)
		return
	}

	// 2. 业务逻辑处理
	service := NewOrderService(ctx)

	// 先根据订单号获取订单ID
	orderModel := service.orderModel
	order, err := orderModel.GetOrderByOrderNo(orderNo)
	if err != nil {
		results.Failed(ctx, "查询订单失败", err.Error())
		return
	}

	if order == nil {
		results.Failed(ctx, "订单不存在", nil)
		return
	}

	result, err := service.GetOrderStatus(int(order.ID))
	if err != nil {
		results.Failed(ctx, "获取订单状态失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取订单状态成功", result, nil)
}

// CloseOrder 关闭订单
func (c *Manager) CloseOrder(ctx *gin.Context) {
	// 1. 获取当前用户信息
	userInfo, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "用户信息获取失败", "未找到用户信息")
		return
	}
	userClaims := userInfo.(*middleware.UserClaims)
	currentUserID := int(userClaims.ID)
	if currentUserID == 0 {
		results.Failed(ctx, "用户信息获取失败", "用户ID无效")
		return
	}

	// 2. 参数校验
	schema := GetCloseOrderSchema()
	validator := jsonschema.NewValidator(schema)

	// 从POST请求体获取所有数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求数据格式错误", err.Error())
		return
	}

	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 获取参数
	orderNo := ""
	if val, ok := validationResult.Data["orderNo"].(string); ok {
		orderNo = val
	}

	reasonForClosure := 0
	if val, ok := validationResult.Data["reason_for_closure"].(float64); ok {
		reasonForClosure = int(val)
	}

	closureRemarks := ""
	if val, ok := validationResult.Data["closure_remarks"].(string); ok {
		closureRemarks = val
	}

	// 4. 权限检查和订单信息获取
	order, _, operatorName, err := CheckOrderOperationPermissionByOrderNo(ctx, orderNo)
	if err != nil {
		results.Failed(ctx, "权限检查失败", err.Error())
		return
	}

	// 5. 验证订单状态 - 所有用户都只能关闭待放款状态的订单
	if !order.IsPendingDisbursement() {
		results.Failed(ctx, "操作不允许", "只能关闭待放款状态的订单")
		return
	}

	// 6. 执行关闭订单操作
	service := NewOrderService(ctx)
	err = service.CloseOrderWithReasonCode(int(order.ID), reasonForClosure, closureRemarks, currentUserID, operatorName)
	if err != nil {
		results.Failed(ctx, "关闭订单失败", err.Error())
		return
	}

	// 8. 返回结果
	results.Success(ctx, "关闭订单成功", nil, nil)
}

// ProcessDisbursement 处理订单放款
func (c *Manager) ProcessDisbursement(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetDisbursementProcessSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 权限检查和订单信息获取
	orderNo := validationResult.Data["orderNo"].(string)
	_, _, _, err := CheckOrderOperationPermissionByOrderNo(ctx, orderNo)
	if err != nil {
		results.Failed(ctx, "权限检查失败", err.Error())
		return
	}

	// 获取当前用户ID
	getuser, _ := ctx.Get("user")
	user := getuser.(*middleware.UserClaims)

	// 3. 业务逻辑处理
	service := NewOrderService(ctx)

	result, err := service.ProcessOrderDisbursement(ctx, orderNo, user)
	if err != nil {
		results.Failed(ctx, "处理放款失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "处理放款成功", result, nil)
}

// GetDisbursementStatus 刷新放款状态
func (c *Manager) GetDisbursementStatus(ctx *gin.Context) {
	// 1. 参数校验
	orderNo := ctx.Query("order_no")
	if orderNo == "" {
		results.Failed(ctx, "订单编号不能为空", nil)
		return
	}

	schema := GetRefreshDisbursementStatusSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(map[string]interface{}{
		"order_no": orderNo,
	})
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 业务逻辑处理
	service := NewOrderService(ctx)
	err := service.RefreshDisbursementStatus(validationResult.Data["order_no"].(string))
	if err != nil {
		results.Failed(ctx, "刷新放款状态失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "刷新放款状态成功", nil, nil)
}

// AssignOrder 批量分配订单给业务员
func (c *Manager) AssignOrder(ctx *gin.Context) {
	// 1. 权限验证 - 只有管理员才能分配订单
	getuser, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "用户信息获取失败", "请重新登录")
		return
	}

	user, ok := getuser.(*middleware.UserClaims)
	if !ok {
		results.Failed(ctx, "用户信息格式错误", "请重新登录")
		return
	}

	// 检查是否为管理员权限
	if !IsAdminUser(ctx) {
		results.Failed(ctx, "权限不足", "只有管理员才能分配订单")
		return
	}

	// 2. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetOrderAssignSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 解析参数
	orderIdsInterface, ok := validationResult.Data["order_ids"]
	if !ok {
		results.Failed(ctx, "参数错误", "order_ids参数缺失")
		return
	}

	// 转换order_ids数组
	orderIdsArray, ok := orderIdsInterface.([]interface{})
	if !ok {
		results.Failed(ctx, "参数错误", "order_ids必须是数组")
		return
	}

	// 限制最多100个订单
	if len(orderIdsArray) > 100 {
		results.Failed(ctx, "参数错误", "最多支持100个订单批量分配")
		return
	}

	if len(orderIdsArray) == 0 {
		results.Failed(ctx, "参数错误", "order_ids不能为空")
		return
	}

	// 转换为int64数组
	var orderIDs []int64
	for i, idInterface := range orderIdsArray {
		switch v := idInterface.(type) {
		case float64:
			if v <= 0 {
				results.Failed(ctx, "参数错误", fmt.Sprintf("第%d个订单ID必须大于0", i+1))
				return
			}
			orderIDs = append(orderIDs, int64(v))
		case int:
			if v <= 0 {
				results.Failed(ctx, "参数错误", fmt.Sprintf("第%d个订单ID必须大于0", i+1))
				return
			}
			orderIDs = append(orderIDs, int64(v))
		default:
			results.Failed(ctx, "参数错误", fmt.Sprintf("第%d个订单ID类型错误", i+1))
			return
		}
	}

	salesID := int(validationResult.Data["sales_id"].(float64))
	operatorID := int(user.ID) // 使用当前登录用户ID作为操作员ID

	// 4. 调用业务逻辑
	service := NewOrderService(ctx)
	responseData := service.BatchAssignOrdersToSales(orderIDs, salesID, operatorID)

	// 5. 返回结果
	successCount := responseData["success_count"].(int)
	failureCount := responseData["failure_count"].(int)
	message := fmt.Sprintf("批量分配完成，成功%d个，失败%d个", successCount, failureCount)
	results.Success(ctx, message, responseData, nil)
}

// ClaimOrder 业务员认领订单
func (c *Manager) ClaimOrder(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetOrderClaimSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewOrderServiceWithOptions(ctx, WithOperationModel())
	orderID := int64(validationResult.Data["orderId"].(float64))
	salesID := int(validationResult.Data["salesId"].(float64))

	err := service.ClaimOrderBySales(orderID, salesID)
	if err != nil {
		results.Failed(ctx, "认领订单失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "认领订单成功", nil, nil)
}

// CancelClaimOrder 取消认领订单
func (c *Manager) CancelClaimOrder(ctx *gin.Context) {
	// 1. 获取当前用户信息
	userInfo, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "用户信息获取失败", "未找到用户信息")
		return
	}
	userClaims := userInfo.(*middleware.UserClaims)
	currentUserID := int(userClaims.ID)
	if currentUserID == 0 {
		results.Failed(ctx, "用户信息获取失败", "用户ID无效")
		return
	}

	// 2. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetOrderCancelClaimSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 获取参数
	orderID := int64(validationResult.Data["order_id"].(float64))

	// 4. 业务逻辑处理
	service := NewOrderServiceWithOptions(ctx, WithOperationModel())
	err := service.CancelClaimOrderBySales(orderID, currentUserID)
	if err != nil {
		results.Failed(ctx, "取消认领订单失败", err.Error())
		return
	}

	// 5. 返回结果
	results.Success(ctx, "取消认领订单成功", nil, nil)
}

// GetPendingOrders 获取待分配订单列表
func (c *Manager) GetPendingOrders(ctx *gin.Context) {
	// 1. 参数校验
	queryData := map[string]interface{}{
		"page":     ctx.Query("page"),
		"pageSize": ctx.Query("pageSize"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range queryData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	schema := GetPendingOrdersListSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewOrderServiceWithOptions(ctx)
	result, err := service.GetPendingOrdersForAssignment(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取待分配订单列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取待分配订单列表成功", result, nil)
}

// UpdateOrderChannel 修改订单渠道
func (c *Manager) UpdateOrderChannel(ctx *gin.Context) {

	// 2. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetOrderChannelUpdateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 获取参数
	orderID := int64(validationResult.Data["order_id"].(float64))
	channelID := int(validationResult.Data["channel_id"].(float64))

	// 4. 权限检查（通过订单ID查询订单信息）
	orderService := NewOrderServiceWithOptions(ctx, WithOrderModel())
	order, err := orderService.orderModel.GetOrderByID(int(orderID))
	if err != nil {
		results.Failed(ctx, "查询订单失败", err.Error())
		return
	}
	if order == nil {
		results.Failed(ctx, "订单不存在", nil)
		return
	}

	_, operatorName, err := CheckOrderOperationPermission(ctx, order)
	if err != nil {
		results.Failed(ctx, "权限检查失败", err.Error())
		return
	}

	// 获取当前用户ID
	getuser, _ := ctx.Get("user")
	user := getuser.(*middleware.UserClaims)
	operatorID := int(user.ID)

	// 5. 业务逻辑处理
	service := NewOrderServiceWithOptions(ctx, WithOrderModel(), WithChannelModelService(), WithOperationModel())

	err = service.UpdateOrderChannel(orderID, channelID, operatorID, operatorName)
	if err != nil {
		results.Failed(ctx, "修改订单渠道失败", err.Error())
		return
	}

	// 6. 返回结果
	results.Success(ctx, "修改订单渠道成功", nil, nil)
}

// GetOrderCustomerInfo 获取下单人信息
func (c *Manager) GetOrderCustomerInfo(ctx *gin.Context) {
	// 1. 参数校验
	orderID := ctx.Query("orderId")
	if orderID == "" {
		results.Failed(ctx, "参数验证失败", "订单ID不能为空")
		return
	}

	queryData := map[string]interface{}{
		"id": orderID,
	}

	schema := GetOrderCustomerInfoSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(queryData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewOrderServiceWithOptions(ctx, WithOrderModel(), WithRemarksModel())
	orderIDInt := convert.MustConvertToInt64(orderID, 0)
	if orderIDInt == 0 {
		results.Failed(ctx, "参数验证失败", "订单ID格式错误")
		return
	}

	result, err := service.GetOrderCustomerInfo(orderIDInt)
	if err != nil {
		results.Failed(ctx, "获取下单人信息失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取下单人信息成功", result, nil)
}

// CreateOrderRemark 新增订单备注
func (c *Manager) CreateOrderRemark(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetOrderRemarkCreateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 获取参数
	orderID := int64(validationResult.Data["order_id"].(float64))
	content := validationResult.Data["content"].(string)

	// 3. 权限检查（通过订单ID查询订单信息）
	orderService := NewOrderService(ctx)
	order, err := orderService.orderModel.GetOrderByID(int(orderID))
	if err != nil {
		results.Failed(ctx, "查询订单失败", err.Error())
		return
	}
	if order == nil {
		results.Failed(ctx, "订单不存在", nil)
		return
	}

	_, _, err = CheckOrderOperationPermission(ctx, order)
	if err != nil {
		results.Failed(ctx, "权限检查失败", err.Error())
		return
	}

	// 获取当前用户ID
	getuser, _ := ctx.Get("user")
	user := getuser.(*middleware.UserClaims)
	userID := user.ID

	// 4. 业务逻辑处理
	service := NewOrderServiceWithOptions(ctx, WithOrderModel())
	err = service.CreateOrderRemark(orderID, content, userID)
	if err != nil {
		results.Failed(ctx, "新增订单备注失败", "fail")
		return
	}

	// 5. 返回结果
	results.Success(ctx, "新增订单备注成功", "success", nil)
}

// EarlySettlement 提前结清订单
func (c *Manager) EarlySettlement(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetOrderEarlySettlementSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 权限检查和订单信息获取
	orderNo := validationResult.Data["orderNo"].(string)
	_, _, operatorName, err := CheckOrderOperationPermissionByOrderNo(ctx, orderNo)
	if err != nil {
		results.Failed(ctx, "权限检查失败", err.Error())
		return
	}

	// 获取当前用户ID用于日志记录
	getuser, _ := ctx.Get("user")
	user := getuser.(*middleware.UserClaims)
	currentUserID := int(user.ID)

	// 3. 业务逻辑处理
	service := NewOrderServiceWithOptions(ctx, WithOrderModel())

	err = service.EarlySettleOrder(orderNo, currentUserID, operatorName)
	if err != nil {
		results.Failed(ctx, "提前结清失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "提前结清成功", nil, nil)
}

// GetOrderBillInfo 获取订单详情页账单信息
func (c *Manager) GetOrderBillInfo(ctx *gin.Context) {
	// 1. 参数校验
	orderNo := ctx.Query("order_no")
	if orderNo == "" {
		results.Failed(ctx, "订单编号不能为空", nil)
		return
	}

	// 参数验证
	schema := GetOrderBillInfoSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(map[string]interface{}{
		"order_no": orderNo,
	})
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 业务逻辑处理
	service := NewOrderService(ctx)
	result, err := service.GetOrderBillInfo(orderNo)
	if err != nil {
		results.Failed(ctx, "获取订单账单信息失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "获取订单账单信息成功", result, nil)
}

// GetOrderPaymentRecords 获取订单支付记录列表
func (c *Manager) GetOrderPaymentRecords(ctx *gin.Context) {
	// 1. 参数校验
	orderNo := ctx.Query("order_no")
	if orderNo == "" {
		results.Failed(ctx, "订单编号不能为空", nil)
		return
	}

	// 获取分页参数
	page := 1
	pageSize := 20
	if pageStr := ctx.Query("page"); pageStr != "" {
		if val := convert.ConvertToInt(pageStr); val != nil && *val > 0 {
			page = *val
		}
	}
	if pageSizeStr := ctx.Query("pageSize"); pageSizeStr != "" {
		if val := convert.ConvertToInt(pageSizeStr); val != nil && *val > 0 && *val <= 100 {
			pageSize = *val
		}
	}

	// 参数验证
	schema := GetOrderPaymentRecordsSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(map[string]interface{}{
		"order_no": orderNo,
		"page":     page,
		"pageSize": pageSize,
	})
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 业务逻辑处理
	service := NewOrderService(ctx)
	result, err := service.GetOrderPaymentRecords(orderNo, page, pageSize)
	if err != nil {
		results.Failed(ctx, "获取订单支付记录失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "获取订单支付记录成功", result, nil)
}

// WaiveBillAmount 减免账单金额
func (c *Manager) WaiveBillAmount(ctx *gin.Context) {
	// 1. 获取当前用户信息
	userInfo, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "用户信息获取失败", "未找到用户信息")
		return
	}
	userClaims := userInfo.(*middleware.UserClaims)
	currentUserID := int(userClaims.ID)
	currentUserName := userClaims.Username
	if currentUserID == 0 {
		results.Failed(ctx, "用户信息获取失败", "用户ID无效")
		return
	}

	// 2. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetWaiveBillAmountSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 业务逻辑处理
	service := NewOrderServiceWithOptions(ctx, WithRepository(), WithOrderModel())
	billID := int(validationResult.Data["bill_id"].(float64))
	waiveAmount := validationResult.Data["waive_amount"].(float64)

	err := service.WaiveBillAmount(billID, waiveAmount, currentUserID, currentUserName)
	if err != nil {
		results.Failed(ctx, "减免账单金额失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "减免账单金额成功", nil, nil)
}

// UpdateBillDueDate 修改账单时间
func (c *Manager) UpdateBillDueDate(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetUpdateBillDueDateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 提取参数
	billID := convert.MustConvertToInt(requestData["bill_id"], 0)
	dueDateStr := convert.ConvertToString(requestData["due_date"])

	if billID == 0 {
		results.Failed(ctx, "参数验证失败", "账单ID格式错误")
		return
	}

	// 3. 业务逻辑处理
	service := NewOrderService(ctx)
	err := service.UpdateBillDueDate(billID, dueDateStr)
	if err != nil {
		results.Failed(ctx, "修改账单时间失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "修改账单时间成功", nil, nil)
}

// GetOrderProgress 获取订单进度
func (c *Manager) GetOrderProgress(ctx *gin.Context) {
	// 1. 参数校验
	orderNo := ctx.Query("order_no")
	if orderNo == "" {
		results.Failed(ctx, "订单编号不能为空", nil)
		return
	}

	queryData := map[string]interface{}{
		"order_no": orderNo,
	}

	schema := GetOrderProgressSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(queryData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewOrderService(ctx)
	result, err := service.GetOrderProgress(orderNo)
	if err != nil {
		results.Failed(ctx, "获取订单进度失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取订单进度成功", result, nil)
}
