package order

import (
	"context"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/pagination"

	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-module/carbon/v2"
)

// Repository 订单数据仓库层
type Repository struct {
	ctx context.Context
}

// NewRepository 创建订单数据仓库实例
func NewRepository(ctx context.Context) *Repository {
	return &Repository{
		ctx: ctx,
	}
}

// GetOrderByOrderNo 根据订单编号获取订单信息
func (r *Repository) GetOrderByOrderNo(orderNo string) (*model.BusinessLoanOrders, error) {
	orderService := model.NewBusinessLoanOrdersService(r.ctx)
	return orderService.GetOrderByOrderNo(orderNo)
}

// GetBillListByOrderID 根据订单ID获取账单列表
func (r *Repository) GetBillListByOrderID(orderID int) ([]BillInfo, error) {
	// 查询账单列表，包含支付时间和累计退款金额
	query := model.DB(model.WithContext(r.ctx)).Table("business_repayment_bills brb").
		Fields(`brb.id,
			brb.period_number,
			brb.total_due_amount,
			brb.paid_amount,
			brb.total_refund_amount,
			brb.status,
			brb.due_date,
			brb.total_waive_amount,
			brb.paid_at as payment_time`).
		Where("brb.order_id", orderID).
		OrderBy("brb.period_number ASC")

	data, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("查询账单列表失败: %v", err)
	}

	var bills []BillInfo
	for _, item := range data {
		bill := BillInfo{
			ID:                convert.GetIntFromMap(item, "id", 0),
			PeriodNumber:      convert.GetIntFromMap(item, "period_number", 0),
			TotalDueAmount:    convert.GetStringFromMap(item, "total_due_amount"),
			PaidAmount:        convert.GetStringFromMap(item, "paid_amount"),
			TotalRefundAmount: convert.GetStringFromMap(item, "total_refund_amount"),
			TotalWaiveAmount:  convert.GetStringFromMap(item, "total_waive_amount"),
			Status:            convert.GetIntFromMap(item, "status", 0),
			DueDate:           convert.GetTimeFromMap(item, "due_date").Format("2006-01-02"),
		}

		// 状态文案
		bill.StatusText = model.GetBillStatusText(bill.Status)

		// 处理支付时间
		if paymentTimeVal := convert.GetTimeFromMap(item, "payment_time"); !paymentTimeVal.IsZero() {
			paymentTime := paymentTimeVal.Format("2006-01-02 15:04:05")
			bill.PaymentTime = &paymentTime
		}

		bills = append(bills, bill)
	}

	return bills, nil
}

// GetDisbursementRecordsByOrderID 根据订单ID获取放款记录
func (r *Repository) GetDisbursementRecordsByOrderID(orderID int) ([]DisbursementRecord, error) {
	query := model.DB().Table("business_payment_transactions").
		Fields(`id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no`).
		Where("order_id", orderID).
		WhereNull("deleted_at").
		Where("type", "DISBURSEMENT").
		OrderBy("created_at ASC")

	data, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("查询放款记录失败: %v", err)
	}

	var records []DisbursementRecord
	for _, item := range data {
		record := DisbursementRecord{
			ID:            convert.GetIntFromMap(item, "id", 0),
			Status:        convert.GetIntFromMap(item, "status", 0),
			PayoutAccount: "统统付小贷", // 占位预留
			PaymentType:   "统统付小贷打款",
			Amount:        convert.GetStringFromMap(item, "amount"),
		}

		// 状态文案
		record.StatusText = model.GetTransactionStatusText(record.Status)

		// 处理发起时间
		if initiatedAtVal := convert.GetTimeFromMap(item, "initiated_at"); !initiatedAtVal.IsZero() {
			record.InitiatedAt = initiatedAtVal.Format("2006-01-02 15:04:05")
		}

		// 处理完成时间
		if completedAtVal := convert.GetTimeFromMap(item, "completed_at"); !completedAtVal.IsZero() {
			completedAt := completedAtVal.Format("2006-01-02 15:04:05")
			record.CompletedAt = &completedAt
		}

		// 处理渠道流水号
		if channelTransactionNo := convert.GetStringFromMap(item, "channel_transaction_no"); channelTransactionNo != "" {
			record.ChannelTransactionNo = &channelTransactionNo
		}

		records = append(records, record)
	}

	return records, nil
}

// GetPaymentRecordsByOrderID 根据订单ID获取支付记录（分页）
func (r *Repository) GetPaymentRecordsByOrderID(orderID, page, pageSize int) (*pagination.PaginationResponse, error) {
	// 构建分页请求
	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 创建查询对象
	baseQuery := model.DB(model.WithContext(r.ctx)).Table("business_payment_transactions bpt").
		Where("bpt.order_id", orderID).
		WhereNull("bpt.deleted_at").
		Where("bpt.type", "IN", []string{"REPAYMENT", "WITHHOLD", "PARTIAL_OFFLINE_REPAYMENT", "MANUAL_WITHHOLD"})

	countQuery := baseQuery
	dataQuery := baseQuery.
		Fields(`bpt.id,
			bpt.transaction_no,
			bpt.amount,
			bpt.status,
			bpt.type,
			bpt.withhold_type,
			bpt.created_at as initiated_at,
			bpt.completed_at,
			bpt.channel_transaction_no,
			bpt.error_message,
			bpt.offline_payment_voucher,
			brb.period_number,
			brb.id as bill_id,
			bpc.channel_name as payment_type,
			(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount`).
		LeftJoin("business_repayment_bills brb", "bpt.bill_id = brb.id").
		LeftJoin("business_payment_channels bpc", "bpt.payment_channel_id = bpc.id").
		OrderBy("bpt.created_at DESC")

	// 使用分页工具函数执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("查询支付记录失败: %v", err)
	}

	// 处理查询结果数据
	processedList := r.processPaymentRecords(result.Data.([]gform.Data))
	result.Data = processedList

	return result, nil
}

// processPaymentRecords 处理支付记录数据
func (r *Repository) processPaymentRecords(data []gform.Data) []PaymentRecord {
	var records []PaymentRecord
	for _, item := range data {
		record := PaymentRecord{
			ID:            convert.GetIntFromMap(item, "id", 0),
			TransactionNo: convert.GetStringFromMap(item, "transaction_no"),
			BillID:        convert.GetIntFromMap(item, "bill_id", 0),
			Amount:        convert.GetStringFromMap(item, "amount"),
			Status:        convert.GetIntFromMap(item, "status", 0),
			RefundAmount:  convert.GetStringFromMap(item, "refund_amount"),
			Image:         "", // 预留字段
		}

		record.StatusText = model.GetTransactionStatusText(record.Status)

		// 处理代扣类型
		record.WithholdType = convert.GetStringFromMap(item, "withhold_type")
		record.WithholdTypeText = model.GetWithholdTypeText(record.WithholdType)

		// 处理支付类型
		paymentType := convert.GetStringFromMap(item, "payment_type")
		if paymentType == "" {
			// 根据type字段设置默认值
			transactionType := convert.GetStringFromMap(item, "type")
			switch transactionType {
			case "REPAYMENT":
				paymentType = "还款"
			case "WITHHOLD":
				paymentType = "代扣"
			case "PARTIAL_OFFLINE_REPAYMENT":
				paymentType = "部分线下还款"
			case "MANUAL_WITHHOLD":
				paymentType = "手动代扣"
			default:
				paymentType = "其他"
			}
		}
		record.PaymentType = paymentType

		// 处理发起时间
		if item["initiated_at"] != nil {
			record.InitiatedAt = item["initiated_at"].(time.Time).Format("2006-01-02 15:04:05")
		}

		// 处理完成时间
		if item["completed_at"] != nil {
			completedAt := item["completed_at"].(time.Time).Format("2006-01-02 15:04:05")
			record.CompletedAt = &completedAt
		}

		// 处理期数
		if periodNumber := convert.GetIntFromMap(item, "period_number", 0); periodNumber > 0 {
			record.PeriodNumber = &periodNumber
		}

		// 处理渠道流水号
		if channelTransactionNo := convert.GetStringFromMap(item, "channel_transaction_no"); channelTransactionNo != "" {
			record.ChannelTransactionNo = channelTransactionNo
		}

		// 处理错误信息
		if errorMessage := convert.GetStringFromMap(item, "error_message"); errorMessage != "" {
			record.ErrorMessage = errorMessage
		}

		// 图片
		if offlinePaymentVoucher := convert.GetStringFromMap(item, "offline_payment_voucher"); offlinePaymentVoucher != "" {
			record.Image = offlinePaymentVoucher
		}

		records = append(records, record)
	}

	return records
}

// GetBillByIDInTransaction 根据账单ID获取账单信息（在事务中查询）
func (r *Repository) GetBillByIDInTransaction(tx gform.IOrm, billID int) (*model.BusinessRepaymentBills, error) {
	var bill model.BusinessRepaymentBills

	data, err := tx.Table("business_repayment_bills").
		Where("id", billID).
		First()

	if err != nil {
		return nil, fmt.Errorf("查询账单失败: %v", err)
	}

	if data == nil {
		return nil, nil
	}

	// 转换数据
	bill.ID = convert.MustConvertToInt(data["id"], 0)
	bill.OrderID = convert.MustConvertToInt(data["order_id"], 0)
	bill.UserID = convert.MustConvertToInt(data["user_id"], 0)
	bill.PeriodNumber = convert.MustConvertToInt(data["period_number"], 0)
	bill.DuePrincipal = model.Decimal(convert.MustConvertToFloat(data["due_principal"], 0))
	bill.DueInterest = model.Decimal(convert.MustConvertToFloat(data["due_interest"], 0))
	bill.DueGuaranteeFee = model.Decimal(convert.MustConvertToFloat(data["due_guarantee_fee"], 0))
	bill.DueOtherFees = model.Decimal(convert.MustConvertToFloat(data["due_other_fees"], 0))
	bill.AssetManagementEntry = model.Decimal(convert.MustConvertToFloat(data["asset_management_entry"], 0))
	bill.LateFee = model.Decimal(convert.MustConvertToFloat(data["late_fee"], 0))
	bill.TotalDueAmount = model.Decimal(convert.MustConvertToFloat(data["total_due_amount"], 0))
	bill.PaidAmount = model.Decimal(convert.MustConvertToFloat(data["paid_amount"], 0))
	bill.Status = convert.MustConvertToInt(data["status"], 0)
	bill.TotalWaiveAmount = model.Decimal(convert.MustConvertToFloat(data["total_waive_amount"], 0))
	bill.DueDate = convert.GetTimeFromMap(data, "due_date")

	return &bill, nil
}

// GetOrderByIDInTransaction 根据订单ID获取订单信息（在事务中查询）
func (r *Repository) GetOrderByIDInTransaction(tx gform.IOrm, orderID int) (*model.BusinessLoanOrders, error) {
	var order model.BusinessLoanOrders

	data, err := tx.Table("business_loan_orders").
		Where("id", orderID).
		First()

	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}

	if data == nil {
		return nil, nil
	}

	// 转换数据
	order.ID = uint(convert.MustConvertToInt(data["id"], 0))
	order.OrderNo = convert.ConvertToString(data["order_no"])
	order.UserID = uint(convert.MustConvertToInt(data["user_id"], 0))
	order.ProductRuleID = uint(convert.MustConvertToInt(data["product_rule_id"], 0))
	order.LoanAmount = model.Decimal(convert.MustConvertToFloat(data["loan_amount"], 0))
	order.Principal = model.Decimal(convert.MustConvertToFloat(data["principal"], 0))
	order.TotalInterest = model.Decimal(convert.MustConvertToFloat(data["total_interest"], 0))
	order.TotalGuaranteeFee = model.Decimal(convert.MustConvertToFloat(data["total_guarantee_fee"], 0))
	order.TotalOtherFees = model.Decimal(convert.MustConvertToFloat(data["total_other_fees"], 0))
	order.TotalRepayableAmount = model.Decimal(convert.MustConvertToFloat(data["total_repayable_amount"], 0))
	order.AmountPaid = model.Decimal(convert.MustConvertToFloat(data["amount_paid"], 0))
	order.Status = int8(convert.MustConvertToInt(data["status"], 0))

	return &order, nil
}

// UpdateBillAmounts 更新账单金额信息
func (r *Repository) UpdateBillAmounts(tx gform.IOrm, billID int, updateData map[string]interface{}) error {
	_, err := tx.Table("business_repayment_bills").
		Where("id", billID).
		Data(updateData).
		Update()

	if err != nil {
		return fmt.Errorf("更新账单金额失败: %v", err)
	}

	return nil
}

// UpdateOrderAmounts 更新订单金额信息
func (r *Repository) UpdateOrderAmounts(tx gform.IOrm, orderID int, updateData map[string]interface{}) error {
	_, err := tx.Table("business_loan_orders").
		Where("id", orderID).
		Data(updateData).
		Update()

	if err != nil {
		return fmt.Errorf("更新订单金额失败: %v", err)
	}

	return nil
}

// CreateOperationLogInTransaction 在事务中创建操作日志
func (r *Repository) CreateOperationLogInTransaction(tx gform.IOrm, orderID int, operatorID int, operatorName, action, details string) error {
	logData := map[string]interface{}{
		"order_id":      orderID,
		"operator_id":   operatorID,
		"operator_name": operatorName,
		"action":        action,
		"details":       details,
		"created_at":    time.Now().Unix(),
	}

	_, err := tx.Table("business_order_operation_logs").
		Data(logData).
		Insert()

	if err != nil {
		return fmt.Errorf("创建操作日志失败: %v", err)
	}

	return nil
}

// UpdateBillDueDate 更新账单到期时间
func (r *Repository) UpdateBillDueDate(billID int, dueDate time.Time) error {
	// 使用model层更新账单到期时间
	billService := model.NewBusinessRepaymentBillsService(r.ctx)

	// 先检查账单是否存在
	bill, err := billService.GetBillByID(billID)
	if err != nil {
		return fmt.Errorf("查询账单失败: %v", err)
	}
	if bill == nil {
		return fmt.Errorf("账单不存在")
	}

	// 判断账单是否可更新到期时间，满足条件 账单状态: 0-待支付；3-逾期待支付；7-部分还款；9-逾期部分支付
	if bill.Status != model.RepaymentBillStatusUnpaid &&
		bill.Status != model.RepaymentBillStatusOverdueUnpaid &&
		bill.Status != model.RepaymentBillStatusPartialPaid &&
		bill.Status != model.RepaymentBillStatusOverduePartialPaid {
		return fmt.Errorf("账单已完结，不可更新到期时间")
	}

	// 更新账单到期时间
	newBillStatus := bill.Status

	carbonDueDate := carbon.CreateFromStdTime(dueDate)
	carbonNow := carbon.Now()

	//  如果账单未支付，修改时间为今天之前，则将账单状态改为逾期未支付
	if carbonDueDate.DiffInDays(carbonNow) > 0 && bill.Status == model.RepaymentBillStatusUnpaid {
		newBillStatus = model.RepaymentBillStatusOverdueUnpaid
	}

	// 如果账单逾期待支付，修改时间为今天或今天以后，则将账单状态改为待支付
	if carbonDueDate.DiffInDays(carbonNow) <= 0 && bill.Status == model.RepaymentBillStatusOverdueUnpaid {
		newBillStatus = model.RepaymentBillStatusUnpaid

	}

	// 如果账单为部分支付，修改时间为今天以前，则将账单状态改为逾期部分支付
	if carbonDueDate.DiffInDays(carbonNow) > 0 && bill.Status == model.RepaymentBillStatusPartialPaid {
		newBillStatus = model.RepaymentBillStatusOverduePartialPaid
	}

	// 如果账单为逾期部分支付，修改时间为今天或今天以后，则将账单状态改为部分支付
	if carbonDueDate.DiffInDays(carbonNow) <= 0 && bill.Status == model.RepaymentBillStatusOverduePartialPaid {
		newBillStatus = model.RepaymentBillStatusPartialPaid
	}

	// 使用原生SQL更新到期时间
	_, err = model.DB().Table("business_repayment_bills").
		Where("id", billID).
		Update(map[string]interface{}{
			"due_date": dueDate,
			"status":   newBillStatus,
		})
	if err != nil {
		return fmt.Errorf("更新账单到期时间失败: %v", err)
	}

	return nil
}

// GetRiskPassedTimeByUserID 根据用户ID获取风控通过时间
func (r *Repository) GetRiskPassedTimeByUserID(userID int) (*string, error) {
	query := `
		SELECT updated_at
		FROM risk_evaluations
		WHERE customer_id = ? AND risk_result = 0
		ORDER BY updated_at DESC
		LIMIT 1
	`

	result, err := model.DB().Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("查询风控评估失败: %v", err)
	}

	if len(result) == 0 {
		// 没有风控通过记录，返回nil
		return nil, nil
	}

	updatedAt, ok := result[0]["updated_at"]
	if !ok || updatedAt == nil {
		return nil, nil
	}

	// 处理时间格式转换
	var timeStr string
	switch v := updatedAt.(type) {
	case string:
		timeStr = v
	case []byte:
		timeStr = string(v)
	default:
		return nil, fmt.Errorf("时间格式错误")
	}

	// 尝试解析时间并格式化
	if parsedTime, err := time.Parse("2006-01-02 15:04:05", timeStr); err == nil {
		formatted := parsedTime.Format("2006-01-02 15:04:05")
		return &formatted, nil
	}

	// 如果解析失败，直接返回原始字符串
	return &timeStr, nil
}

// GetCustomerNameByUserID 根据用户ID获取客户姓名
func (r *Repository) GetCustomerNameByUserID(userID int) (string, error) {
	query := `
		SELECT name
		FROM business_app_account
		WHERE id = ?
	`

	result, err := model.DB().Query(query, userID)
	if err != nil {
		return "", fmt.Errorf("查询客户信息失败: %v", err)
	}

	if len(result) == 0 {
		return "", fmt.Errorf("客户不存在")
	}

	name, ok := result[0]["name"].(string)
	if !ok {
		return "", fmt.Errorf("客户姓名格式错误")
	}

	return name, nil
}

// GetSalesAssigneeNameByID 根据业务员ID获取业务员名称
func (r *Repository) GetSalesAssigneeNameByID(salesAssigneeID int) (string, error) {
	query := `
		SELECT name
		FROM business_account
		WHERE id = ?
	`

	result, err := model.DB().Query(query, salesAssigneeID)
	if err != nil {
		return "", fmt.Errorf("查询业务员信息失败: %v", err)
	}

	if len(result) == 0 {
		return "", fmt.Errorf("业务员不存在")
	}

	name, ok := result[0]["name"].(string)
	if !ok {
		return "", fmt.Errorf("业务员姓名格式错误")
	}

	return name, nil
}

// GetOrderListWithFilters 获取订单列表（包含账单到期时间筛选）
func (r *Repository) GetOrderListWithFilters(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 创建两个独立的查询对象，避免Count()污染数据查询
	countQuery := r.buildOrderListQuery(params)
	dataQuery := r.buildOrderListQuery(params).
		Fields(`blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,
		blo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,
		blo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,
		blo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,
	    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,
		blo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,
		baa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,
		c1.channel_name as channel_name, bpc.channel_name as payment_channel_name,
		c2.channel_name as initial_order_channel_name,
		pr.loan_period as loan_period, pr.total_periods as total_periods,
		 sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,
		(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods`).
		OrderBy("blo.id DESC")

	// 使用分页工具函数执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("获取订单列表失败: %v", err)
	}

	return result, nil
}

// buildOrderListQuery 构建订单列表查询对象
func (r *Repository) buildOrderListQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB().Table("business_loan_orders blo")

	// 添加关联查询
	query = query.
		LeftJoin("business_app_account baa", "blo.user_id = baa.id").
		LeftJoin("channel c1", "blo.channel_id = c1.id").
		LeftJoin("channel c2", "baa.channelId = c2.id").
		LeftJoin("product_rules pr", "blo.product_rule_id = pr.id").
		LeftJoin("business_payment_channels bpc", "blo.payment_channel_id = bpc.id").
		LeftJoin("business_account sales_user", "blo.sales_assignee_id = sales_user.id").
		LeftJoin("business_account collection_user", "blo.collection_assignee_id = collection_user.id")

	// 应用筛选条件
	query = r.applyOrderListFilters(query, params)

	return query
}

// applyOrderListFilters 应用订单列表筛选条件
func (r *Repository) applyOrderListFilters(query gform.IOrm, params map[string]interface{}) gform.IOrm {
	// 订单编号查询
	if orderNo, ok := params["order_no"]; ok && orderNo != "" {
		query = query.Where("blo.order_no", "like", fmt.Sprintf("%%%v%%", orderNo))
	}

	// 用户ID查询
	if userID, ok := params["user_id"]; ok && userID != "" {
		query = query.Where("blo.user_id", userID)
	}

	// 用户姓名查询
	if userName, ok := params["user_name"]; ok && userName != "" {
		query = query.Where("baa.name", "like", fmt.Sprintf("%%%v%%", userName))
	}

	// 用户身份证号查询
	if userIDCard, ok := params["user_id_card"]; ok && userIDCard != "" {
		query = query.Where("baa.idCard", "like", fmt.Sprintf("%%%v%%", userIDCard))
	}

	// 用户手机号查询
	if userMobile, ok := params["user_mobile"]; ok && userMobile != "" {
		query = query.Where("baa.mobile", "like", fmt.Sprintf("%%%v%%", userMobile))
	}

	// 业务员姓名查询
	if salesAssigneeName, ok := params["sales_assignee_name"]; ok && salesAssigneeName != "" {
		query = query.Where("sales_user.name", "like", fmt.Sprintf("%%%v%%", salesAssigneeName))
	}

	// 产品规则ID查询
	if productRuleID, ok := params["product_rule_id"]; ok && productRuleID != "" {
		query = query.Where("blo.product_rule_id", productRuleID)
	}

	// 借款金额范围查询
	if loanAmountMin, ok := params["loan_amount_min"]; ok && loanAmountMin != "" {
		query = query.Where("blo.loan_amount", ">=", loanAmountMin)
	}

	if loanAmountMax, ok := params["loan_amount_max"]; ok && loanAmountMax != "" {
		query = query.Where("blo.loan_amount", "<=", loanAmountMax)
	}

	// 订单状态查询
	if status, ok := params["status"]; ok && status != "" {
		query = query.Where("blo.status", status)
	}

	// 关单原因查询
	if reasonForClosure, ok := params["reason_for_closure"]; ok && reasonForClosure != "" {
		query = query.Where("blo.reason_for_closure", reasonForClosure)
	}

	// 冻结状态查询
	if isFreeze, ok := params["is_freeze"]; ok && isFreeze != "" {
		query = query.Where("blo.is_freeze", isFreeze)
	}

	// 投诉状态查询
	if complaintStatus, ok := params["complaint_status"]; ok && complaintStatus != "" {
		query = query.Where("blo.complaint_status", complaintStatus)
	}

	// 复审状态查询
	if reviewStatus, ok := params["review_status"]; ok && reviewStatus != "" {
		query = query.Where("blo.review_status", reviewStatus)
	}

	// 业务员ID查询
	if salesAssigneeID, ok := params["sales_assignee_id"]; ok && salesAssigneeID != "" {
		query = query.Where("blo.sales_assignee_id", salesAssigneeID)
	}

	// 是否已分配业务员查询
	if isSalesAssigned, ok := params["is_sales_assigned"]; ok && isSalesAssigned != "" {
		assignedVal := convert.MustConvertToInt(isSalesAssigned, -1)
		switch assignedVal {
		case 1:
			// 已分配：sales_assignee_id IS NOT NULL AND sales_assignee_id > 0
			query = query.Where("blo.sales_assignee_id IS NOT NULL AND blo.sales_assignee_id > 0")
		case 0:
			// 未分配：sales_assignee_id IS NULL OR sales_assignee_id = 0
			query = query.Where("(blo.sales_assignee_id IS NULL OR blo.sales_assignee_id = 0)")
		}
	}

	// 催收员ID查询
	if collectionAssigneeID, ok := params["collection_assignee_id"]; ok && collectionAssigneeID != "" {
		query = query.Where("blo.collection_assignee_id", collectionAssigneeID)
	}

	// 渠道ID查询
	if channelID, ok := params["channel_id"]; ok && channelID != "" {
		query = query.Where("blo.channel_id", channelID)
	}

	// 初始订单渠道ID查询
	if initialOrderChannelID, ok := params["initial_order_channel_id"]; ok && initialOrderChannelID != "" {
		query = query.Where("baa.channelId", initialOrderChannelID)
	}

	// 客户来源查询
	if customerOrigin, ok := params["customer_origin"]; ok && customerOrigin != "" {
		query = query.Where("blo.customer_origin", "like", fmt.Sprintf("%%%v%%", customerOrigin))
	}

	// 支付渠道ID查询
	if paymentChannelID, ok := params["payment_channel_id"]; ok && paymentChannelID != "" {
		query = query.Where("blo.payment_channel_id", paymentChannelID)
	}

	// 是否需要退款查询
	if isRefundNeeded, ok := params["is_refund_needed"]; ok && isRefundNeeded != "" {
		query = query.Where("blo.is_refund_needed", isRefundNeeded)
	}

	// 创建时间范围查询（对应SQL表中的created_at字段）
	if createdAtStart, ok := params["created_at_start"]; ok && createdAtStart != "" {
		t := carbon.Parse(createdAtStart.(string)).StartOfDay().StdTime()
		if !t.IsZero() {
			query = query.Where("blo.created_at", ">=", t)
		}
	}

	if createdAtEnd, ok := params["created_at_end"]; ok && createdAtEnd != "" {
		t := carbon.Parse(createdAtEnd.(string)).EndOfDay().StdTime()
		if !t.IsZero() {
			query = query.Where("blo.created_at", "<=", t)
		}
	}

	// 兼容submitted_at参数名（映射到created_at）
	if submittedAtStart, ok := params["submitted_at_start"]; ok && submittedAtStart != "" {
		t := carbon.Parse(submittedAtStart.(string)).StartOfDay().StdTime()
		if !t.IsZero() {
			query = query.Where("blo.created_at", ">=", t)
		}
	}

	if submittedAtEnd, ok := params["submitted_at_end"]; ok && submittedAtEnd != "" {
		t := carbon.Parse(submittedAtEnd.(string)).EndOfDay().StdTime()
		if !t.IsZero() {
			query = query.Where("blo.created_at", "<=", t)
		}
	}

	// 放款时间范围查询
	if disbursedAtStart, ok := params["disbursed_at_start"]; ok && disbursedAtStart != "" {
		query = query.Where("blo.disbursed_at", ">=", disbursedAtStart)
	}

	if disbursedAtEnd, ok := params["disbursed_at_end"]; ok && disbursedAtEnd != "" {
		query = query.Where("blo.disbursed_at", "<=", disbursedAtEnd)
	}

	// 完成时间范围查询
	if completedAtStart, ok := params["completed_at_start"]; ok && completedAtStart != "" {
		query = query.Where("blo.completed_at", ">=", completedAtStart)
	}

	if completedAtEnd, ok := params["completed_at_end"]; ok && completedAtEnd != "" {
		query = query.Where("blo.completed_at", "<=", completedAtEnd)
	}

	// 账单到期时间筛选 - 使用EXISTS子查询
	billDueDateStart, hasBillStart := params["bill_due_date_start"]
	billDueDateEnd, hasBillEnd := params["bill_due_date_end"]

	if (hasBillStart && billDueDateStart != "") || (hasBillEnd && billDueDateEnd != "") {
		existsQuery := "EXISTS (SELECT 1 FROM business_repayment_bills brb WHERE brb.order_id = blo.id"

		if hasBillStart && billDueDateStart != "" {
			existsQuery += " AND brb.due_date >= '" + fmt.Sprintf("%v", billDueDateStart) + "'"
		}

		if hasBillEnd && billDueDateEnd != "" {
			existsQuery += " AND brb.due_date <= '" + fmt.Sprintf("%v", billDueDateEnd) + "'"
		}

		existsQuery += ")"
		query = query.Where(existsQuery)
	}

	return query
}

// GetRiskScoresBatch 批量获取风控分数
func (r *Repository) GetRiskScoresBatch(userIDs []int) (map[int]int, error) {
	result := make(map[int]int)

	// 如果用户ID列表为空，直接返回空结果
	if len(userIDs) == 0 {
		return result, nil
	}

	// 构建IN查询条件
	userIDsStr := make([]string, len(userIDs))
	for i, id := range userIDs {
		userIDsStr[i] = fmt.Sprintf("%d", id)
	}

	// 查询每个用户最新的风控评估记录
	query := `
		SELECT re.customer_id, re.risk_score
		FROM risk_evaluations re
		INNER JOIN (
			SELECT customer_id, MAX(created_at) as latest_time
			FROM risk_evaluations
			WHERE customer_id IN (` + strings.Join(userIDsStr, ",") + `)
			GROUP BY customer_id
		) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time
	`

	riskScores, err := model.DB().Query(query)
	if err != nil {
		return nil, fmt.Errorf("批量查询风控分数失败: %v", err)
	}

	// 将查询结果转换为map
	for _, item := range riskScores {
		customerID := convert.GetIntFromMap(item, "customer_id", 0)
		riskScore := convert.GetIntFromMap(item, "risk_score", 0)
		if customerID > 0 {
			result[customerID] = riskScore
		}
	}

	return result, nil
}

// UpdateUserRemainingAmount 更新用户剩余额度,可以是占用或者撤回
func (r *Repository) UpdateUserRemainingAmount(tx gform.IOrm, userID int, loanAmout float64, isWithdraw bool) error {
	// 查询用户剩余额
	modelService := model.NewBusinessAppAccountService()
	reminderQuota, err := modelService.GetReminderQuotaByUserID(int64(userID))
	if err != nil {
		return fmt.Errorf("查询用户剩余额度失败: %v", err)
	}

	var handler gform.IOrm
	if tx != nil {
		handler = tx
	} else {
		handler = model.DB(model.WithContext(r.ctx))

	}

	var remainingAmount float64
	if isWithdraw {
		remainingAmount = reminderQuota + loanAmout
	} else {
		remainingAmount = reminderQuota - loanAmout
	}

	// 更新用户剩余额度
	num, err := handler.Table("business_app_account").
		Where("id", userID).
		Data(map[string]interface{}{
			"reminderQuota": remainingAmount,
		}).
		Update()

	if err != nil {
		return fmt.Errorf("更新用户剩余额度失败: %v", err)
	}

	if num == 0 {
		return fmt.Errorf("更新用户剩余额度失败: 用户不存在")
	}

	return nil
}

// GetOrderListOptimized 获取订单列表
// 使用协程并行查询和查询结构优化来提升性能
func (r *Repository) GetOrderListOptimized(params map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 使用sync.WaitGroup管理协程同步
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 错误收集
	var errors []error
	addError := func(err error) {
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			errors = append(errors, err)
		}
	}

	// 结果存储
	var mainData []gform.Data
	var totalCount int64
	var assigneeData map[int]map[string]interface{}
	var paidPeriodsData map[int]int

	// 1. 并行执行主查询（订单基础信息 + 用户信息 + 主要渠道信息）
	wg.Add(1)
	go func() {
		defer wg.Done()

		// 构建主查询
		mainQuery := r.buildOptimizedMainQuery(params)
		dataQuery := mainQuery.
			Fields(`blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,
			blo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,
			blo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,
			blo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,
			blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,								
			blo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at, blo.completed_at, blo.contract_id,
			baa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,
			c1.channel_name as channel_name, bpc.channel_name as payment_channel_name,
			c2.channel_name as initial_order_channel_name,
			pr.loan_period as loan_period, pr.total_periods as total_periods`).
			OrderBy("blo.id DESC")

		// 执行主数据查询
		result, err := dataQuery.Offset(paginationReq.GetOffset()).Limit(paginationReq.GetLimit()).Get()
		if err != nil {
			addError(fmt.Errorf("主查询失败: %v", err))
			return
		}

		mu.Lock()
		mainData = result
		mu.Unlock()
	}()

	// 2. 并行执行总数查询
	wg.Add(1)
	go func() {
		defer wg.Done()

		countQuery := r.buildOptimizedMainQuery(params)
		total, err := countQuery.Count()
		if err != nil {
			addError(fmt.Errorf("总数查询失败: %v", err))
			return
		}

		mu.Lock()
		totalCount = total
		mu.Unlock()
	}()

	// 等待主查询完成，获取订单ID列表
	wg.Wait()

	// 检查是否有错误
	if len(errors) > 0 {
		return nil, fmt.Errorf("查询失败: %v", errors[0])
	}

	// 如果没有数据，直接返回
	if len(mainData) == 0 {
		return &pagination.PaginationResponse{
			Data:       mainData,
			Total:      totalCount,
			Page:       paginationReq.Page,
			PageSize:   paginationReq.PageSize,
			TotalPages: int(math.Ceil(float64(totalCount) / float64(paginationReq.PageSize))),
		}, nil
	}

	// 提取订单ID和业务员ID列表
	orderIDs := make([]int, 0, len(mainData))
	assigneeIDs := make(map[int]bool)

	for _, row := range mainData {
		if id, ok := row["id"].(int64); ok {
			orderIDs = append(orderIDs, int(id))
		}

		// 收集业务员ID
		if salesID, ok := row["sales_assignee_id"]; ok && salesID != nil {
			if id, ok := salesID.(int64); ok && id > 0 {
				assigneeIDs[int(id)] = true
			}
		}
		if collectionID, ok := row["collection_assignee_id"]; ok && collectionID != nil {
			if id, ok := collectionID.(int64); ok && id > 0 {
				assigneeIDs[int(id)] = true
			}
		}
	}

	// 3. 并行查询业务员信息
	wg.Add(1)
	go func() {
		defer wg.Done()

		if len(assigneeIDs) == 0 {
			mu.Lock()
			assigneeData = make(map[int]map[string]interface{})
			mu.Unlock()
			return
		}

		assigneeIDList := make([]int, 0, len(assigneeIDs))
		for id := range assigneeIDs {
			assigneeIDList = append(assigneeIDList, id)
		}

		result, err := model.DB().Table("business_account").
			Fields("id, name").
			Where("id", "in", assigneeIDList).
			Get()
		if err != nil {
			addError(fmt.Errorf("业务员信息查询失败: %v", err))
			return
		}

		data := make(map[int]map[string]interface{})
		for _, row := range result {
			if id, ok := row["id"].(int64); ok {
				data[int(id)] = row
			}
		}

		mu.Lock()
		assigneeData = data
		mu.Unlock()
	}()

	// 4. 并行查询已付期数
	wg.Add(1)
	go func() {
		defer wg.Done()

		if len(orderIDs) == 0 {
			mu.Lock()
			paidPeriodsData = make(map[int]int)
			mu.Unlock()
			return
		}

		result, err := model.DB().Table("business_repayment_bills").
			Fields("order_id, COUNT(*) as paid_count").
			Where("order_id", "in", orderIDs).
			Where("status", 1).
			GroupBy("order_id").
			Get()
		if err != nil {
			addError(fmt.Errorf("已付期数查询失败: %v", err))
			return
		}

		data := make(map[int]int)
		for _, row := range result {
			if orderID, ok := row["order_id"].(int64); ok {
				if count, ok := row["paid_count"].(int64); ok {
					data[int(orderID)] = int(count)
				}
			}
		}

		mu.Lock()
		paidPeriodsData = data
		mu.Unlock()
	}()

	// 等待所有并行查询完成
	wg.Wait()

	// 检查是否有错误
	if len(errors) > 0 {
		return nil, fmt.Errorf("并行查询失败: %v", errors[0])
	}

	// 5. 组装最终结果
	finalData := r.assembleOrderListData(mainData, assigneeData, paidPeriodsData)

	// 计算总页数
	totalPages := int(math.Ceil(float64(totalCount) / float64(paginationReq.PageSize)))

	return &pagination.PaginationResponse{
		Data:       finalData,
		Total:      totalCount,
		Page:       paginationReq.Page,
		PageSize:   paginationReq.PageSize,
		TotalPages: totalPages,
	}, nil
}

// buildOptimizedMainQuery 构建优化的主查询
func (r *Repository) buildOptimizedMainQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB().Table("business_loan_orders blo")

	// 只保留必要的关联查询
	query = query.
		LeftJoin("business_app_account baa", "blo.user_id = baa.id").
		LeftJoin("channel c1", "blo.channel_id = c1.id").
		LeftJoin("channel c2", "baa.channelId = c2.id").
		LeftJoin("product_rules pr", "blo.product_rule_id = pr.id").
		LeftJoin("business_payment_channels bpc", "blo.payment_channel_id = bpc.id")

	// 应用筛选条件
	query = r.applyOrderListFilters(query, params)

	return query
}

// assembleOrderListData 组装订单列表数据
func (r *Repository) assembleOrderListData(
	mainData []gform.Data,
	assigneeData map[int]map[string]interface{},
	paidPeriodsData map[int]int,
) []gform.Data {

	for i, row := range mainData {
		// 添加业务员姓名
		if salesID, ok := row["sales_assignee_id"]; ok && salesID != nil {
			if id, ok := salesID.(int64); ok && id > 0 {
				if assignee, exists := assigneeData[int(id)]; exists {
					if name, ok := assignee["name"]; ok {
						mainData[i]["sales_assignee_name"] = name
					}
				}
			}
		}

		if collectionID, ok := row["collection_assignee_id"]; ok && collectionID != nil {
			if id, ok := collectionID.(int64); ok && id > 0 {
				if assignee, exists := assigneeData[int(id)]; exists {
					if name, ok := assignee["name"]; ok {
						mainData[i]["collection_assignee_name"] = name
					}
				}
			}
		}

		// 添加已付期数
		if orderID, ok := row["id"].(int64); ok {
			if paidCount, exists := paidPeriodsData[int(orderID)]; exists {
				mainData[i]["paid_periods"] = paidCount
			} else {
				mainData[i]["paid_periods"] = 0
			}
		}

		// 确保所有字段都有默认值
		if _, exists := mainData[i]["audit_assignee_name"]; !exists {
			mainData[i]["audit_assignee_name"] = nil
		}
		if _, exists := mainData[i]["sales_assignee_name"]; !exists {
			mainData[i]["sales_assignee_name"] = nil
		}
		if _, exists := mainData[i]["collection_assignee_name"]; !exists {
			mainData[i]["collection_assignee_name"] = nil
		}
	}

	return mainData
}

// GetAccountByID 根据ID获取业务应用账户详情
func (r *Repository) GetAccountByID(tx gform.IOrm, id int64) (account *model.BusinessAppAccount, err error) {
	var handler gform.IOrm
	if tx != nil {
		handler = tx
	} else {
		handler = model.DB(model.WithContext(r.ctx))
	}
	userInfo, err := handler.Table("business_app_account").Where("id", id).First()
	if err != nil {
		return
	}

	err = gconv.Struct(userInfo, &account)
	if err != nil {
		return nil, err
	}
	return account, nil
}
