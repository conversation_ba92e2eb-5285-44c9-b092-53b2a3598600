package risk

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"time"

	"fincore/app/business/blacklist"
	"fincore/app/business/channel"
	"fincore/app/business/risk/external"
	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/riskmodelservice"
	"fincore/thirdparty/riskthirdparty"
	"fincore/utils/gform"
	"fincore/utils/lock"

	"go.uber.org/zap"
)

// RiskEvaluationRequest 风控评估请求
type RiskEvaluationRequest struct {
	CustomerID uint64 `json:"customer_id" binding:"required"`
}

// RiskEvaluationResponse 风控评估响应
type RiskEvaluationResponse struct {
	RiskReportID         string  `json:"risk_report_id"`           // 风控报告ID
	RiskScore            int     `json:"risk_score"`               // 风控分数
	RiskResult           int     `json:"risk_result"`              // 风控结果
	AvailableCreditLimit float64 `json:"available_credit_limit"`   // 客户可用授信额度
	EvaluationTime       string  `json:"evaluation_time"`          // 评估时间
	FailureType          *string `json:"failure_type,omitempty"`   // 失败类型
	FailureReason        *string `json:"failure_reason,omitempty"` // 失败原因
}

// LoanProductsRequest 贷款产品匹配请求
type LoanProductsRequest struct {
	CustomerID uint64 `form:"customer_id" binding:"required"`
	ChannelID  uint64 `form:"channel_id" binding:"required"`
}

// LoanProduct 贷款产品信息
type LoanProduct struct {
	ProductID          int     `json:"product_id"`
	ProductName        string  `json:"product_name"`
	LoanAmount         float64 `json:"loan_amount"`
	LoanPeriod         int     `json:"loan_period"`
	AnnualInterestRate float64 `json:"annual_interest_rate"`
	InterestRate       float64 `json:"interest_rate"`
	RepaymentMethod    string  `json:"repayment_method"`
	ChannelName        string  `json:"channel_name"`
	ChannelID          uint64  `json:"channel_id"`
	MaxAmount          float64 `json:"max_amount"`
	MinAmount          float64 `json:"min_amount"`
	Term               int     `json:"term"`
	MinRiskScore       float64 `json:"min_risk_score"`
	MaxRiskScore       float64 `json:"max_risk_score"`
}

// LoanProductsResponse 贷款产品匹配响应
type LoanProductsResponse struct {
	OverallCreditLimit   float64               `json:"overall_credit_limit"`   // 客户授信额度
	AvailableCreditLimit float64               `json:"available_credit_limit"` // 客户可用授信额度
	Products             []*model.ProductRules `json:"products"`
}

// RiskReportResponse 风控报告响应
type RiskReportResponse struct {
	EvaluationID   string                 `json:"evaluation_id"`
	RiskScore      int                    `json:"risk_score"`
	RiskResult     int                    `json:"risk_result"`
	EvaluationTime string                 `json:"evaluation_time"`
	RawData        map[string]interface{} `json:"raw_data"`
}

// RiskService 风控服务
type RiskService struct {
	riskEvalService           *model.RiskEvaluationService
	productService            *model.ProductRulesService
	channelService            *channel.ChannelService
	thirdPartyService         *riskthirdparty.RiskThirdPartyService
	riskModelService          *riskmodelservice.RiskModelService
	blacklistService          *blacklist.BlacklistService
	businessAppAccountService *model.BusinessAppAccountService
}

// NewRiskService 创建风控服务实例
func NewRiskService(
	ctx context.Context,
) *RiskService {
	return &RiskService{
		riskEvalService:           model.NewRiskEvaluationService(),
		productService:            model.NewProductRulesService(),
		channelService:            channel.NewChannelService(),
		thirdPartyService:         riskthirdparty.NewRiskThirdPartyService(),
		riskModelService:          riskmodelservice.NewRiskModelService(),
		blacklistService:          blacklist.NewBlacklistService(ctx),
		businessAppAccountService: model.NewBusinessAppAccountService(),
	}
}

// checkBlacklist 检查黑名单并返回拒绝响应（如果命中）
func (s *RiskService) checkBlacklist(ctx context.Context, customerInfo *model.BusinessAppAccount) *RiskEvaluationResponse {
	blacklistResult, err := s.blacklistService.CheckUserBlacklist(ctx, uint64(customerInfo.ID), customerInfo)
	if err != nil {
		global.App.Log.Error("黑名单检查失败", zap.Error(err))
		return nil // 黑名单检查失败，继续后续流程
	}

	if !blacklistResult.IsBlacklisted {
		return nil // 未命中黑名单
	}

	// 命中黑名单，记录日志并返回拒绝结果
	global.App.Log.Info("用户命中内部黑名单",
		zap.Uint64("customer_id", uint64(customerInfo.ID)),
		zap.String("blacklist_type", string(blacklistResult.BlacklistType)),
		zap.Any("reasons", blacklistResult.Reasons))

	evaluationID := model.GenerateEvaluationID(int(customerInfo.ID))
	failureType := riskmodelservice.FailureTypeInternalBlacklist
	failureReason := fmt.Sprintf("%v", blacklistResult.Details)

	// 存储黑名单拒绝的评估结果
	evaluation := &model.RiskEvaluation{
		EvaluationID:   evaluationID,
		CustomerID:     int(customerInfo.ID),
		RiskScore:      0,
		RiskResult:     model.REJECTED,
		EvaluationTime: time.Now(),
		FailureType:    &failureType,
		FailureReason:  &failureReason,
	}

	if err := s.riskEvalService.CreateEvaluation(evaluation); err != nil {
		global.App.Log.Error("存储黑名单拒绝结果失败", zap.Error(err))
	}

	return &RiskEvaluationResponse{
		RiskReportID:   evaluationID,
		RiskScore:      0,
		RiskResult:     model.REJECTED,
		EvaluationTime: evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
		FailureType:    &failureType,
		FailureReason:  &failureReason,
	}
}

// evaluateRiskModel 调用风控模型并处理结果
func (s *RiskService) evaluateWithOriginalModel(ctx context.Context, db gform.IOrm, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	evaluationID := model.GenerateEvaluationID(int(customerInfo.ID))
	// 构建风控请求参数
	riskReq := &riskthirdparty.RiskRequest{
		Name:   customerInfo.Name,
		IDNo:   customerInfo.IDCard,
		Mobile: customerInfo.Mobile,
	}

	fmt.Printf("获取到的客户信息: %s %s %s\n", customerInfo.Name, customerInfo.IDCard, customerInfo.Mobile)

	// 调用风控模型服务
	modelResult, err := s.riskModelService.ProcessRiskEvaluationWithCustomer(ctx, int(customerInfo.ID), riskReq)
	if err != nil {
		return nil, fmt.Errorf("风控模型评估失败: %v", err)
	}

	// 存储第三方风控原始数据（非外部黑名单情况）
	if modelResult.FailureType != riskmodelservice.FailureTypeExternalBlacklist {
		if err := s.storeThirdPartyRawData(evaluationID, modelResult); err != nil {
			fmt.Printf("存储第三方原始数据失败: %v\n", err)
		}
	}

	// 存储评估结果
	evaluation := s.buildEvaluation(evaluationID, uint64(customerInfo.ID), modelResult)
	if err := s.riskEvalService.CreateEvaluation(evaluation); err != nil {
		return nil, fmt.Errorf("存储评估结果失败: %v", err)
	}

	// 调用统一的后处理逻辑
	return s.processEvaluationResult(ctx, db, evaluation, customerInfo)
}

// buildEvaluation 构建评估结果对象
func (s *RiskService) buildEvaluation(evaluationID string, customerID uint64, modelResult *riskmodelservice.RiskEvaluationResult) *model.RiskEvaluation {
	evaluation := &model.RiskEvaluation{
		EvaluationID:   evaluationID,
		CustomerID:     int(customerID),
		RiskScore:      int(modelResult.FinalScore),
		RiskResult:     modelResult.FinalResult,
		EvaluationTime: time.Now(),
	}

	// 设置失败类型和原因
	if modelResult.FinalResult == model.REJECTED {
		if modelResult.FailureType != "" {
			evaluation.FailureType = &modelResult.FailureType
			evaluation.FailureReason = &modelResult.FailureReason
		} else if modelResult.FinalScore < 710 {
			failureType := riskmodelservice.FailureTypeRiskScore
			failureReason := "风控分数过低"
			evaluation.FailureType = &failureType
			evaluation.FailureReason = &failureReason
		}
	}

	return evaluation
}

// processEvaluationResult 处理评估结果的后续逻辑
func (s *RiskService) processEvaluationResult(ctx context.Context, db gform.IOrm, evaluation *model.RiskEvaluation, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	// 检查渠道ID，如果是渠道1 不管风控结果怎么样 都改成通过 而且风控分数改成 1000
	if customerInfo.ChannelID == model.OfflineChannel {
		evaluation.RiskResult = model.REVIEW
		evaluation.RiskScore = model.MaxRiskScore
	}

	if customerInfo.AvailableProductID != model.NoChange {
		evaluation.RiskResult = model.REVIEW
		evaluation.RiskScore = model.MaxRiskScore
	}

	response, err := s.getProductsAndMaxCreditByScore(ctx, uint64(customerInfo.ChannelID), float64(evaluation.RiskScore), customerInfo)
	if err != nil {
		return nil, err
	}

	// 使用通用函数计算额度信息
	quotaResult := calculateQuotaInfo(customerInfo.AllQuota, customerInfo.ReminderQuota, response.OverallCreditLimit)
	allQuote := quotaResult.NewAllQuota
	remainingQuota := quotaResult.AvailableQuota

	// 使用事务统一更新额度（只有在需要更新时才执行）
	if allQuote != customerInfo.AllQuota || customerInfo.ReminderQuota != remainingQuota {
		if err := s.businessAppAccountService.UpdateQuotaAndRiskScoreWithTx(db, int64(evaluation.CustomerID), allQuote, remainingQuota, evaluation.RiskScore); err != nil {
			global.App.Log.Error("更新额度和风控分数失败", zap.Error(err), zap.Int("customer_id", evaluation.CustomerID), zap.Float64("new_all_quota", allQuote), zap.Float64("new_remaining_quota", remainingQuota), zap.Int("new_risk_score", evaluation.RiskScore))
			return nil, err
		}

	}
	response.AvailableCreditLimit = remainingQuota
	return &RiskEvaluationResponse{
		RiskReportID:         evaluation.EvaluationID,
		RiskScore:            evaluation.RiskScore,
		RiskResult:           evaluation.RiskResult,
		AvailableCreditLimit: response.AvailableCreditLimit,
		EvaluationTime:       evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
		FailureType:          evaluation.FailureType,
		FailureReason:        evaluation.FailureReason,
	}, nil
}

// evaluateWithThirdPartyService 使用第三方风控服务进行评估

func (s *RiskService) evaluateWithThirdPartyService(ctx context.Context, db gform.IOrm, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	// 创建第三方风控服务实例
	thirdService := external.NewGoDemoRiskService()

	// 调用第三方风控服务
	thirdResult, err := thirdService.EvaluateRisk(ctx, customerInfo)
	if err != nil {
		global.App.Log.Error("第三方风控服务调用失败",
			zap.Uint64("customer_id", uint64(customerInfo.ID)),
			zap.Error(err))
		//构造thirdResult
		thirdResult = &external.GoDemoResult{
			AuditResult:  "MANUAL",
			LeidaV4Data:  "",
			TanZhenCData: "",
			ZwscData:     "",
			CreatedAt:    time.Now(),
		}
	}

	// 生成评估ID
	evaluationID := model.GenerateEvaluationID(int(customerInfo.ID))

	// 映射风控结果
	riskResult, riskScore, failureReason := thirdService.MapToRiskResult(thirdResult)

	// 存储原始数据到risk_raw_data表
	if err := s.storeThirdPartyRawDataFromExternal(evaluationID, thirdResult); err != nil {
		global.App.Log.Error("存储第三方风控原始数据失败",
			zap.String("evaluation_id", evaluationID),
			zap.Error(err))
		// 不阻断流程，继续执行
	}

	// 构建评估结果
	evaluation := &model.RiskEvaluation{
		EvaluationID:   evaluationID,
		CustomerID:     int(customerInfo.ID),
		RiskScore:      riskScore,
		RiskResult:     riskResult,
		EvaluationTime: time.Now(),
	}

	// 如果有失败原因，设置失败信息
	if failureReason != "" {
		failureType := "THIRD_PARTY_REJECT"
		evaluation.FailureType = &failureType
		evaluation.FailureReason = &failureReason
	}

	// 存储评估结果到risk_evaluations表
	if err := s.riskEvalService.CreateEvaluation(evaluation); err != nil {
		global.App.Log.Error("存储第三方风控评估结果失败",
			zap.String("evaluation_id", evaluationID),
			zap.Error(err))
		return nil, fmt.Errorf("存储评估结果失败: %v", err)
	}

	// 调用统一的后处理逻辑
	return s.processEvaluationResult(ctx, db, evaluation, customerInfo)
}

// storeThirdPartyRawDataFromExternal 存储第三方风控原始数据
func (s *RiskService) storeThirdPartyRawDataFromExternal(evaluationID string, result *external.GoDemoResult) error {
	// 构建原始数据
	rawData := &model.RiskRawData{
		EvaluationID:     evaluationID,
		LeidaV4Response:  result.LeidaV4Data,
		TanZhenCResponse: result.TanZhenCData,
		ZwscResponse:     result.ZwscData,
		DataSource:       "third_party",
		CreatedAt:        result.CreatedAt,
		UpdatedAt:        result.CreatedAt,
	}

	// 存储到数据库
	return s.riskEvalService.CreateRawData(rawData)
}

// EvaluateRisk 执行基础风控评估（仅通过customerID）
func (s *RiskService) EvaluateRisk(ctx context.Context, db gform.IOrm, customerID int64) (*RiskEvaluationResponse, error) {
	customerInfo, err := s.GetCustomerInfo(ctx, customerID)
	if err != nil {
		return nil, err
	}
	// 验证必要字段
	if customerInfo.Name == "" || customerInfo.IDCard == "" || customerInfo.Mobile == "" {
		return nil, fmt.Errorf("客户信息不完整: 姓名、身份证号或手机号为空")
	}

	// 2. 查询数据库中的最新评估记录
	riskEvalService := model.NewRiskEvaluationService()
	latestEvaluation, err := riskEvalService.GetLatestEvaluationByCustomerID(int(customerID), int(customerInfo.ChannelID), customerInfo.AvailableProductID)
	if err != nil {
		global.App.Log.Error("查询最新评估记录失败", zap.Error(err))
	}

	// 声明返回变量
	var rs *RiskEvaluationResponse

	// 如果有有效的评估记录，直接使用
	if err == nil && latestEvaluation != nil {
		rs = &RiskEvaluationResponse{
			RiskReportID:   latestEvaluation.EvaluationID,
			RiskScore:      latestEvaluation.RiskScore,
			RiskResult:     latestEvaluation.RiskResult,
			EvaluationTime: latestEvaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
			FailureType:    latestEvaluation.FailureType,
		}
	} else {
		// 没有有效评估记录，执行重新评估
		rs, err = s.PerformNewEvaluation(ctx, db, customerInfo)
		if err != nil {
			return nil, err
		}
	}

	// 后续修改结果都要执行
	if err == nil && customerInfo.AvailableProductID != model.NoChange {
		rs.RiskResult = model.APPROVED
		rs.RiskScore = model.MaxRiskScore
	}
	if err == nil && customerInfo.ChannelID == model.OfflineChannel {
		rs.RiskResult = model.APPROVED
		rs.RiskScore = model.MaxRiskScore
	}

	return rs, err
}

// PerformNewEvaluation 执行新的风控评估
func (s *RiskService) PerformNewEvaluation(ctx context.Context, db gform.IOrm, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {

	// 1. 获取第三方风控配置
	thirdRiskConfig, err := external.GetThirdRiskConfig()
	if err != nil {
		global.App.Log.Error("获取第三方风控配置失败", zap.Error(err))
		// 配置获取失败时使用原有风控模型
		thirdRiskConfig = nil
	}
	// 2. 执行风控评估
	if thirdRiskConfig != nil && thirdRiskConfig.Enabled {
		// 检查用户状态是否为2（黑名单）
		if customerInfo.Status == model.StatusTypeBlacklist {
			global.App.Log.Warn("用户状态为手动拉黑黑名单", zap.Uint64("customer_id", uint64(customerInfo.ID)), zap.Int("status", customerInfo.Status))
			//构造返回结果
			failureType := string(blacklist.ReasonBlacklist)
			failureReason := "用户状态为手动拉黑黑名单"

			// 构造评估记录
			evaluationID := model.GenerateEvaluationID(int(customerInfo.ID))
			evaluation := &model.RiskEvaluation{
				EvaluationID:   evaluationID,
				CustomerID:     int(customerInfo.ID),
				RiskScore:      0,
				RiskResult:     model.REJECTED,
				FailureType:    &failureType,
				FailureReason:  &failureReason,
				EvaluationTime: time.Now(),
			}

			s.riskEvalService.CreateEvaluation(evaluation)
			return &RiskEvaluationResponse{
				RiskScore:     0,
				RiskResult:    model.REJECTED,
				FailureType:   &failureType,
				FailureReason: &failureReason,
			}, nil
		}
		// 使用第三方风控服务
		return s.evaluateWithThirdPartyService(ctx, db, customerInfo)
	} else {
		// 执行黑名单检查
		if blacklistResponse := s.checkBlacklist(ctx, customerInfo); blacklistResponse != nil {
			return blacklistResponse, nil
		}

		// 使用原有风控模型
		return s.evaluateWithOriginalModel(ctx, db, customerInfo)
	}
}

type EvaluateRiskWithProductParams struct {
	CustomerID uint64 `json:"customer_id" binding:"required"` // 客户ID
	ProductID  uint64 `json:"product_id" binding:"required"`  // 产品ID
	ChannelID  uint64 `json:"channel_id" binding:"required"`  // 渠道ID
}

// EvaluateRiskWithProduct 执行风控评估并匹配产品（通过customerID、productID和channelID）
func (s *RiskService) EvaluateRiskWithProduct(ctx context.Context, params EvaluateRiskWithProductParams) (*RiskEvaluationResponse, error) {
	return s.EvaluateRiskWithProductTx(ctx, model.DB(), params)
}

// evaluateRiskWithProductInternal 执行风控评估并匹配产品的内部实现
func (s *RiskService) EvaluateRiskWithProductTx(ctx context.Context, db gform.IOrm, params EvaluateRiskWithProductParams) (*RiskEvaluationResponse, error) {
	// 获取客户信息
	customerInfo, err := s.GetCustomerInfo(ctx, int64(params.CustomerID))
	if err != nil {
		return nil, err
	}
	riskEvalService := model.NewRiskEvaluationService()
	latestEvaluation, err := riskEvalService.GetLatestEvaluationByCustomerID(int(customerInfo.ID), int(customerInfo.ChannelID), customerInfo.AvailableProductID)
	if err != nil {
		global.App.Log.Error("查询最新评估记录失败", zap.Error(err))
	}
	baseEvaluation := &RiskEvaluationResponse{}
	if err == nil && latestEvaluation != nil && isEvaluationValid(latestEvaluation.EvaluationTime) {
		baseEvaluation = &RiskEvaluationResponse{
			RiskReportID:   latestEvaluation.EvaluationID,
			RiskScore:      latestEvaluation.RiskScore,
			RiskResult:     latestEvaluation.RiskResult,
			EvaluationTime: latestEvaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
			FailureType:    latestEvaluation.FailureType,
		}
	} else {
		// 没有有效记录或记录已过期，执行重新评估
		newEvaluation, err := s.PerformNewEvaluation(ctx, db, customerInfo)
		if err != nil {
			return nil, err
		}
		baseEvaluation = newEvaluation
	}

	return s.processProductEvaluation(ctx, db, params, baseEvaluation, customerInfo)
}

// GetProductsByAmount 根据额度获取匹配的产品列表
func (s *RiskService) GetProductsByAmount(ctx context.Context, loanAmount float64) ([]*model.ProductRules, error) {
	global.App.Log.Info("开始根据额度获取产品列表", zap.Float64("loanAmount", loanAmount))

	// 1. 直接从产品表获取所有产品
	productRulesService := &model.ProductRulesService{}
	allProducts, err := productRulesService.GetActiveProductRules()
	if err != nil {
		return nil, fmt.Errorf("获取产品列表失败: %v", err)
	}

	// 2. 根据额度过滤产品规则
	var matchedProducts []*model.ProductRules
	for _, product := range allProducts {
		global.App.Log.Debug("检查产品额度匹配",
			zap.Int("productID", product.ID),
			zap.String("ruleName", product.RuleName),
			zap.Float64("productAmount", product.LoanAmount),
			zap.Float64("requestAmount", loanAmount))

		// 额度匹配：产品额度小于等于请求额度
		if product.LoanAmount <= loanAmount {
			matchedProducts = append(matchedProducts, &product)
			global.App.Log.Info("产品额度匹配成功",
				zap.Int("productID", product.ID),
				zap.String("ruleName", product.RuleName))
		}
	}

	global.App.Log.Info("成功获取匹配的产品规则", zap.Int("count", len(matchedProducts)))
	return matchedProducts, nil
}

// GetLoanProducts 获取贷款产品列表
func (s *RiskService) GetLoanProducts(ctx context.Context, customerID int64) (*LoanProductsResponse, error) {
	// 1. 参数验证
	if customerID == 0 {
		return nil, fmt.Errorf("客户ID不能为空")
	}

	// 2. 获取客户信息
	customerInfo, err := s.GetCustomerInfo(ctx, customerID)
	if err != nil {
		return nil, fmt.Errorf("获取客户信息失败: %v", err)
	}

	// 3. 从客户信息中获取渠道ID
	channelID := customerInfo.ChannelID

	// 获取用户锁，确保同一用户查询到结果唯一
	key := fmt.Sprintf("user_loan_lock_%d", customerID)
	loanLock := lock.GetLock(key)
	loanLock.Lock()
	global.App.Log.Info("获取用户贷款产品加锁成功", zap.Int64("customer_id", customerID), zap.String("lock_key", key))
	defer loanLock.Unlock()

	// 查询风险评估
	riskEvaluation, err := s.EvaluateRisk(ctx, model.DB(), customerID)
	if err != nil {
		return nil, err
	}

	// 3. 检查风控结果
	if riskEvaluation.RiskResult == model.REJECTED {
		response := &LoanProductsResponse{
			OverallCreditLimit: 0,
			Products:           []*model.ProductRules{},
		}
		return response, nil
	}

	// 4. 获取风险分数
	riskScore := riskEvaluation.RiskScore

	// 5. 通过风险分数获取产品和最大额度
	response, err := s.getProductsAndMaxCreditByScore(ctx, uint64(channelID), float64(riskScore), customerInfo)
	if err != nil {
		return nil, err
	}

	// 6. 使用已获取的客户信息计算可用额度
	response.AvailableCreditLimit = math.Min(float64(customerInfo.ReminderQuota), response.AvailableCreditLimit)

	// 9. 如果可用额度为0，直接返回空产品列表
	if response.AvailableCreditLimit == 0 {
		response.Products = []*model.ProductRules{}
		return response, nil
	}

	// 10. 过滤掉超出可用额度的产品（使用更高效的切片过滤）
	filteredProducts := make([]*model.ProductRules, 0, len(response.Products))
	for _, product := range response.Products {
		if product.LoanAmount <= response.AvailableCreditLimit {
			filteredProducts = append(filteredProducts, product)
		}
	}
	response.Products = filteredProducts
	return response, nil
}

// GetRiskReports 获取风控报告列表（支持时间查询）
func (s *RiskService) GetRiskReports(ctx context.Context, data map[string]interface{}) ([]RiskReportResponse, error) {
	// 1. 参数转换
	customerID, err := GetCustomerIDFromData(data)
	if err != nil {
		return nil, err
	}

	// 2. 获取时间参数
	startDate := ""
	endDate := ""
	if val, ok := data["start_date"]; ok {
		if str, ok := val.(string); ok {
			startDate = str
		}
	}
	if val, ok := data["end_date"]; ok {
		if str, ok := val.(string); ok {
			endDate = str
		}
	}

	// 3. 获取风控评估记录
	evaluations, err := s.riskEvalService.GetEvaluationsByCustomerIDAndDateRange(int(customerID), startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("未找到风控评估记录: %v", err)
	}

	if len(evaluations) == 0 {
		return []RiskReportResponse{}, nil
	}

	// 5. 构建响应数据
	var reports []RiskReportResponse
	for _, evaluation := range evaluations {
		// 获取原始数据
		rawData, err := s.riskEvalService.GetRawDataByEvaluationID(evaluation.EvaluationID)
		var rawDataMap map[string]interface{}
		rawDataMap = make(map[string]interface{})

		if err == nil {
			// 解析雷达V4数据
			if rawData.LeidaV4Response != "" {
				var leidaData map[string]interface{}
				if err := json.Unmarshal([]byte(rawData.LeidaV4Response), &leidaData); err == nil {
					rawDataMap["leida_v4"] = leidaData
				}
			}

			// 解析探真C数据
			if rawData.TanZhenCResponse != "" {
				var tanZhenData map[string]interface{}
				if err := json.Unmarshal([]byte(rawData.TanZhenCResponse), &tanZhenData); err == nil {
					rawDataMap["tan_zhen_c"] = tanZhenData
				}
			}

			// 解析中网数据
			if rawData.ZwscResponse != "" {
				var zwscData map[string]interface{}
				if err := json.Unmarshal([]byte(rawData.ZwscResponse), &zwscData); err == nil {
					rawDataMap["zwsc"] = zwscData
				}
			}
		}

		report := RiskReportResponse{
			EvaluationID:   evaluation.EvaluationID,
			RiskScore:      evaluation.RiskScore,
			RiskResult:     evaluation.RiskResult,
			EvaluationTime: evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
			RawData:        rawDataMap,
		}
		reports = append(reports, report)
	}

	return reports, nil
}

// getCustomerInfo 从数据库获取客户信息
func (s *RiskService) GetCustomerInfo(ctx context.Context, customerID int64) (*model.BusinessAppAccount, error) {
	// 从数据库查询用户信息
	customerInfo, err := s.businessAppAccountService.GetBusinessAppAccountByID(customerID)
	if err != nil {
		return nil, fmt.Errorf("查询账户失败: %v", err)
	}

	return customerInfo, nil
}

// getMatchingProducts 获取匹配的贷款产品
func (s *RiskService) getMatchingProducts(ctx context.Context, channelID uint64, riskScore float64) ([]*model.ProductRules, error) {
	global.App.Log.Info("开始获取匹配的产品规则", zap.Uint64("channelID", channelID), zap.Float64("riskScore", riskScore))

	// 1. 从数据库获取渠道信息
	channelService := model.NewChannelService()
	var channels []model.Channel
	var err error

	if channelID == 0 {
		// 获取所有活跃渠道
		channels, err = channelService.GetActiveChannels()
	} else {
		// 获取指定渠道
		channel, err := channelService.GetChannelByID(int(channelID))
		if err != nil {
			return nil, fmt.Errorf("查询渠道失败: %v", err)
		}
		if channel != nil && channel.ChannelStatus == model.ChannelStatusEnabled {
			channels = append(channels, *channel)
		}
	}
	global.App.Log.Info("获取到渠道数量", zap.Int("count", len(channels)))

	if err != nil {
		return nil, fmt.Errorf("查询渠道失败: %v", err)
	}

	if len(channels) == 0 {
		return []*model.ProductRules{}, nil
	}

	// 2. 根据渠道的loan_rules匹配产品规则ID
	var matchedRuleIDs []uint64
	for _, channel := range channels {
		// 解析loan_rules字段
		if channel.LoanRules == "" {
			continue
		}

		var loanRules []LoanRule
		if err := json.Unmarshal([]byte(channel.LoanRules), &loanRules); err != nil {
			global.App.Log.Warn("解析渠道loan_rules失败", zap.Int("channelID", channel.ID), zap.Error(err))
			continue
		}

		// 根据风险分数匹配规则
		for _, rule := range loanRules {
			global.App.Log.Debug("检查规则匹配",
				zap.Uint64("ruleID", rule.RuleID),
				zap.Float64("minRiskScore", rule.MinRiskScore),
				zap.Float64("maxRiskScore", rule.MaxRiskScore),
				zap.Float64("currentRiskScore", riskScore))

			// 风险分数在规则范围内则匹配
			if riskScore >= rule.MinRiskScore {
				matchedRuleIDs = append(matchedRuleIDs, rule.RuleID)
				global.App.Log.Info("规则匹配成功", zap.Uint64("ruleID", rule.RuleID))
			}
		}
	}

	if len(matchedRuleIDs) == 0 {
		global.App.Log.Info("未找到匹配的产品规则")
		return []*model.ProductRules{}, nil
	}

	// 3. 根据规则ID获取产品
	productRulesService := &model.ProductRulesService{}
	productRulesSlice, err := productRulesService.GetProductRulesByIDs(matchedRuleIDs)
	if err != nil {
		return nil, fmt.Errorf("查询产品规则失败: %v", err)
	}

	global.App.Log.Info("成功获取匹配的产品规则", zap.Int("count", len(productRulesSlice)))
	return productRulesSlice, nil
}

// getProductsAndMaxCreditByScore 通过风险分数获取对应的产品和最大额度
func (s *RiskService) getProductsAndMaxCreditByScore(ctx context.Context, channelID uint64, riskScore float64, customerInfo *model.BusinessAppAccount) (*LoanProductsResponse, error) {
	// 1. 如果availableProductID不为0，直接使用该产品ID匹配产品
	if customerInfo.AvailableProductID != 0 {
		productRulesService := &model.ProductRulesService{}
		product, err := productRulesService.GetProductRuleByID(customerInfo.AvailableProductID)
		if err != nil {
			return nil, fmt.Errorf("根据availableProductID查询产品失败: %v", err)
		}

		// 构建响应，使用单个产品
		response := &LoanProductsResponse{
			OverallCreditLimit:   product.LoanAmount,
			AvailableCreditLimit: product.LoanAmount,
			Products:             []*model.ProductRules{product},
		}
		return response, nil
	}

	// 2. 查询匹配的产品（原有逻辑）
	products, err := s.getMatchingProducts(ctx, channelID, riskScore)
	if err != nil {
		return nil, fmt.Errorf("查询产品失败: %v", err)
	}

	// 2. 计算总授信额度 - 取products中最大的LoanAmount
	var maxLoanAmount float64
	for _, product := range products {
		if product.LoanAmount > maxLoanAmount {
			maxLoanAmount = product.LoanAmount
		}
	}

	// 3. 构建响应
	response := &LoanProductsResponse{
		OverallCreditLimit:   maxLoanAmount,
		AvailableCreditLimit: maxLoanAmount,
		Products:             products,
	}

	return response, nil
}

// 模拟的第三方服务结构体
type RiskModelResult struct {
	RiskScore   float64 `json:"risk_score"`
	RiskResult  int     `json:"risk_result"`
	CreditLimit float64 `json:"credit_limit"`
}

// ChannelInfo 渠道信息结构体
type ChannelInfo struct {
	ID           int     `json:"id"`
	ChannelName  string  `json:"channel_name"`
	ChannelID    uint64  `json:"channel_id"`
	InterestRate float64 `json:"interest_rate"`
	MaxAmount    float64 `json:"max_amount"`
	MinAmount    float64 `json:"min_amount"`
	Term         int     `json:"term"`
	MinRiskScore float64 `json:"min_risk_score"`
	MaxRiskScore float64 `json:"max_risk_score"`
}

// LoanRule 放款规则结构
type LoanRule struct {
	RuleID       uint64  `json:"rule_id"`
	MinRiskScore float64 `json:"min_risk_score"`
	MaxRiskScore float64 `json:"max_risk_score"`
}

// processProductEvaluation 处理产品评估的通用逻辑
// 如果风控结果被拒绝，直接返回
func (s *RiskService) processProductEvaluation(ctx context.Context, db gform.IOrm, params EvaluateRiskWithProductParams, baseEvaluation *RiskEvaluationResponse, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	if baseEvaluation.RiskResult == model.REJECTED {
		return baseEvaluation, nil
	}
	// 通过风险分数获取匹配的产品和最大额度
	productsResponse, err := s.getProductsAndMaxCreditByScore(ctx, params.ChannelID, float64(baseEvaluation.RiskScore), customerInfo)
	if err != nil {
		return nil, fmt.Errorf("获取产品匹配失败: %v", err)
	}

	// 检查指定的产品是否在匹配列表中
	matchedProduct := findProductByID(productsResponse.Products, int(params.ProductID))
	if matchedProduct == nil {
		return nil, fmt.Errorf("指定的产品ID %d 不符合当前风险评估条件", params.ProductID)
	}

	// 从business_app_account表获取账户信息
	businessAppAccountService := model.NewBusinessAppAccountService()
	account, err := businessAppAccountService.GetBusinessAppAccountByID(int64(params.CustomerID))
	if err != nil {
		return nil, fmt.Errorf("获取账户信息失败: %v", err)
	}

	// 使用通用函数计算额度信息
	quotaResult := calculateQuotaInfo(account.AllQuota, account.ReminderQuota, productsResponse.OverallCreditLimit)
	newAllQuota := quotaResult.NewAllQuota
	currentAvailableQuota := quotaResult.AvailableQuota

	// 检查产品额度是否超过可用额度
	if currentAvailableQuota <= matchedProduct.LoanAmount {
		baseEvaluation.RiskResult = model.REJECTED
		failureType := "insufficient_credit_limit"
		failureReason := fmt.Sprintf("可用额度不足，需要%.2f，可用%.2f", matchedProduct.LoanAmount, currentAvailableQuota)
		baseEvaluation.FailureType = &failureType
		baseEvaluation.FailureReason = &failureReason
		global.App.Log.Info("产品额度超过可用额度，拒绝放款",
			zap.Uint64("customer_id", params.CustomerID),
			zap.Float64("available_credit_limit", currentAvailableQuota),
			zap.Float64("matched_product_loan_amount", matchedProduct.LoanAmount))
		return baseEvaluation, nil
	}

	// 计算扣除产品额度后的新可用额度
	newReminderQuota := currentAvailableQuota - matchedProduct.LoanAmount

	// 使用事务方法统一更新额度
	if newAllQuota != account.AllQuota || newReminderQuota != account.ReminderQuota {
		if err := s.businessAppAccountService.UpdateQuotaAndRiskScoreWithTx(db, int64(params.CustomerID), newAllQuota, newReminderQuota, int(baseEvaluation.RiskScore)); err != nil {
			global.App.Log.Error("更新额度和风控分数失败",
				zap.Error(err),
				zap.Int64("customer_id", int64(params.CustomerID)),
				zap.Float64("new_all_quota", newAllQuota),
				zap.Float64("new_remaining_quota", newReminderQuota),
				zap.Int("new_risk_score", int(baseEvaluation.RiskScore)))
			return nil, err
		}
		global.App.Log.Info("更新用户额度成功",
			zap.Uint64("customer_id", params.CustomerID),
			zap.Float64("old_all_quota", account.AllQuota),
			zap.Float64("new_all_quota", newAllQuota),
			zap.Float64("old_reminder_quota", account.ReminderQuota),
			zap.Float64("new_reminder_quota", newReminderQuota))

	}
	if customerInfo.AvailableProductID != model.NoChange {
		//恢复用户的可用产品的id 为不可用
		if err := s.businessAppAccountService.UpdateAvailableProductIDWithTx(db, int64(params.CustomerID), model.NoChange); err != nil {
			global.App.Log.Error("更新用户可用产品ID失败",
				zap.Error(err),
				zap.Int64("customer_id", int64(params.CustomerID)),
				zap.Int64("available_product_id", int64(params.ProductID)))
			return nil, err
		}
	}

	// 设置可用额度并返回结果
	baseEvaluation.AvailableCreditLimit = newReminderQuota
	return baseEvaluation, nil
}

// storeThirdPartyRawData 存储第三方风控原始数据
func (s *RiskService) storeThirdPartyRawData(evaluationID string, modelResult *riskmodelservice.RiskEvaluationResult) error {
	rawData := &model.RiskRawData{
		EvaluationID: evaluationID,
		DataSource:   "xiamengbangzu", // 合并数据源
	}

	// 存储tan_zhen_c原始数据
	if tanZhenData, exists := modelResult.ThirdPartyResults["tanZhen"]; exists {
		tanZhenJSON, _ := json.Marshal(tanZhenData)
		rawData.TanZhenCResponse = string(tanZhenJSON)
	}

	// 存储leida_v4原始数据
	if leidaData, exists := modelResult.ThirdPartyResults["leida"]; exists {
		leidaJSON, _ := json.Marshal(leidaData)
		rawData.LeidaV4Response = string(leidaJSON)
	}

	// 只有当至少有一个数据源有数据时才存储
	if rawData.TanZhenCResponse != "" || rawData.LeidaV4Response != "" {
		err := s.riskEvalService.CreateRawData(rawData)
		if err != nil {
			return fmt.Errorf("存储第三方原始数据失败: %v", err)
		}
	}

	return nil
}
