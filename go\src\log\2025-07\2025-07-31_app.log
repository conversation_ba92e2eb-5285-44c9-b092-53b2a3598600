{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.234]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.265]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-07-31 23:02:45.915]","caller":"contract/service.go:68","msg":"开始检查合同状态，用户ID: 108, 产品ID: 15"}
{"level":"dev.info","ts":"[2025-07-31 23:02:46.200]","caller":"risk/service.go:481","msg":"产品额度超过可用额度，拒绝放款","customer_id":108,"available_credit_limit":1000,"matched_product_loan_amount":2000}
{"level":"dev.error","ts":"[2025-07-31 23:03:16.292]","caller":"risk/service.go:428","msg":"第三方风控服务调用失败","customer_id":108,"error":"调用第三方风控接口失败: failed to send request: failed to send request: Post \"https://www.litecommerce.com/\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)","stacktrace":"fincore/app/business/risk.(*RiskService).PerformNewEvaluation\n\tD:/work/code/fincore/go/src/app/business/risk/service.go:428\nfincore/app/business/order.(*Service).RefreshRiskData\n\tD:/work/code/fincore/go/src/app/business/order/service.go:542\nfincore/app/business/order.(*Service).CloseOrder.func1\n\tD:/work/code/fincore/go/src/app/business/order/management_service.go:565\nfincore/utils/gform.(*Orm).Transaction\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:379\nfincore/app/business/order.(*Service).CloseOrder\n\tD:/work/code/fincore/go/src/app/business/order/management_service.go:544\nfincore/app/business/order.(*Service).CloseOrderWithReasonCode\n\tD:/work/code/fincore/go/src/app/business/order/management_service.go:476\nfincore/app/business/order.(*Service).CloseOrderWithReason\n\tD:/work/code/fincore/go/src/app/business/order/management_service.go:458\nfincore/app/business/order.(*Service).CreateOrder\n\tD:/work/code/fincore/go/src/app/business/order/creation_service.go:112\nfincore/app/uniapp/order.(*Index).CreateOrder\n\tD:/work/code/fincore/go/src/app/uniapp/order/controller.go:122\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.ErrorLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
