package orders

import (
	"context"
	"fmt"
	"time"

	"fincore/app/business/order"
	"fincore/app/scheduler/tasks"
	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/log"
)

// DisbursementStatusSyncCompensationTask 放款状态同步补偿任务
// 用于处理订单状态为待放款但已有已提交放款流水的补偿场景
type DisbursementStatusSyncCompensationTask struct {
	*tasks.BaseTask
	logger *log.Logger
	ctx    context.Context
}

// NewDisbursementStatusSyncCompensationTask 创建放款状态同步补偿任务
func NewDisbursementStatusSyncCompensationTask() *DisbursementStatusSyncCompensationTask {
	baseTask := tasks.NewBaseTask(
		"disbursement-status-sync-compensation",
		"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景",
		"0 */5 * * * *", // 每5分钟执行一次
		5*time.Minute,   // 超时时间5分钟
	)

	// 设置为单例模式，避免重复执行
	baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
	// 设置重试次数和间隔
	baseTask.SetRetryCount(3).SetRetryInterval(30 * time.Second)

	logger := log.RegisterModule("disbursement_sync_task", "放款状态同步任务")
	ctx := context.Background()
	return &DisbursementStatusSyncCompensationTask{
		BaseTask: baseTask,
		logger:   logger.WithContext(ctx),
	}
}

// Execute 执行放款状态同步补偿任务
func (t *DisbursementStatusSyncCompensationTask) Execute(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "start_execution"),
	).Info("开始执行放款状态同步补偿任务")

	startTime := time.Now()
	var processedCount, successCount, failureCount int

	// 查询符合条件的订单
	orders, err := t.getPendingDisbursementOrdersWithSubmittedTransactions()
	if err != nil {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "query_orders"),
			log.String("error", err.Error()),
		).Error("查询待同步状态的订单失败")
		return fmt.Errorf("查询待同步状态的订单失败: %v", err)
	}

	if len(orders) == 0 {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "no_orders_found"),
		).Info("未找到需要同步状态的订单")
		return nil
	}

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "found_orders"),
		log.Int("order_count", len(orders)),
	).Info("找到需要同步状态的订单")

	// 遍历处理每个订单
	for _, orderData := range orders {
		processedCount++

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("operation", "context_cancelled"),
				log.Int("processed_count", processedCount-1),
			).Warn("任务被取消，停止处理")
			return ctx.Err()
		default:
		}

		orderNo := convert.GetStringFromMap(orderData, "order_no")
		orderID := convert.GetIntFromMap(orderData, "id", 0)

		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("order_no", orderNo),
			log.Int("order_id", orderID),
			log.String("operation", "sync_status"),
		).Info("开始同步订单状态")

		// 调用状态同步方法
		err := t.syncOrderDisbursementStatus(orderNo)
		if err != nil {
			failureCount++
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("order_no", orderNo),
				log.Int("order_id", orderID),
				log.String("operation", "sync_status_failed"),
				log.String("error", err.Error()),
			).Error("订单状态同步失败")
			continue
		}

		successCount++
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("order_no", orderNo),
			log.Int("order_id", orderID),
			log.String("operation", "sync_status_success"),
		).Info("订单状态同步成功")
	}

	// 记录执行统计
	duration := time.Since(startTime)
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "execution_completed"),
		log.Int("processed_count", processedCount),
		log.Int("success_count", successCount),
		log.Int("failure_count", failureCount),
		log.String("duration", duration.String()),
	).Info("放款状态同步补偿任务执行完成")

	return nil
}

// getPendingDisbursementOrdersWithSubmittedTransactions 获取待放款但已有已提交流水的订单
func (t *DisbursementStatusSyncCompensationTask) getPendingDisbursementOrdersWithSubmittedTransactions() ([]gform.Data, error) {
	// 查询条件：
	// 1. 订单状态为待放款 (status = 0)
	// 2. 该订单已经生成了放款流水 (type = 'DISBURSEMENT')
	// 3. 流水状态为已提交 (status = 1)
	// 4. 流水有渠道流水号 (channel_transaction_no != '')
	query := `
		SELECT DISTINCT
			blo.id,
			blo.order_no,
			blo.user_id,
			blo.channel_id,
			blo.loan_amount,
			blo.created_at,
			bpt.transaction_no,
			bpt.channel_transaction_no,
			bpt.status as transaction_status
		FROM business_loan_orders blo
		INNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no
		WHERE blo.status = ?
		  AND bpt.type = ?
		  AND bpt.status = ?
		  AND bpt.channel_transaction_no != ''
		  AND bpt.channel_transaction_no IS NOT NULL
		  AND bpt.deleted_at IS NULL
		ORDER BY blo.created_at ASC
		LIMIT 100
	`

	result, err := model.DB(model.WithContext(t.ctx)).Query(query,
		model.OrderStatusPendingDisbursement,
		model.TransactionTypeDisbursement,
		model.TransactionStatusSubmitted,
	)
	if err != nil {
		return nil, fmt.Errorf("查询数据库失败: %v", err)
	}

	return result, nil
}

// syncOrderDisbursementStatus 同步订单放款状态
// 复用 RefreshDisbursementStatus 方法的核心逻辑
func (t *DisbursementStatusSyncCompensationTask) syncOrderDisbursementStatus(orderNo string) (err error) {
	orderService := order.NewOrderServiceWithOptions(
		t.ctx,
		order.WithBankCardModel(),
		order.WithLogger(t.logger),
		order.WithTransactionModel(),
		order.WithOrderModel(),
	)
	err = orderService.RefreshDisbursementStatus(orderNo)
	if err != nil {
		return fmt.Errorf("刷新放款状态失败: %v", err)
	}
	return
}

// OnStart 任务开始执行前的回调
func (t *DisbursementStatusSyncCompensationTask) OnStart(ctx context.Context) error {
	// task_ 开头，记录整个周期所有 sql 执行日志
	requestID := "task_" + t.GetName() + "_" + time.Now().Format("**************")
	t.ctx = context.WithValue(t.ctx, log.RequestIDKey, requestID)
	t.logger = t.logger.WithRequestID(requestID)
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_starting"),
	).Info("放款状态同步补偿任务即将开始")
	return nil
}

// OnSuccess 任务执行成功后的回调
func (t *DisbursementStatusSyncCompensationTask) OnSuccess(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_success"),
	).Info("放款状态同步补偿任务执行成功")
	return nil
}

// OnError 任务执行失败后的回调
func (t *DisbursementStatusSyncCompensationTask) OnError(ctx context.Context, err error) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_error"),
		log.String("error", err.Error()),
	).Error("放款状态同步补偿任务执行失败")
	return nil
}
