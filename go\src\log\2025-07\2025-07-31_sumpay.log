{"level":"dev.info","ts":"[2025-07-31 23:02:50.260]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250731230250","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250731230250\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.269]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250731230250","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:fYlZNdgJLrmhisUw+Cs+jUyyme9kY00jDmrinnvm51Saparybu7dnv+CEpyWCF/JZy/cWDaLkzr5dyF1QHxqxDT8IoyiOSv5ObV7hWw5PZ+WvWfAj+EL6uiVUMra/UkjbeC8oDwXUR2ZA2yRrU1lV8hZbxDN3WC6Y26CPeP24VTd8HGYjGAhu7kYg0fj6L2gaGWU1Rb+BF/Y2LmhCGvP0nW3+zhkMgsIcjy7RE4aU66l3zExpQJm8ZNMnbjiubbKtPq+zpl6zihfd7/GnrZEypfiu0OBlA4Lz6bcQHmmCOSWlRrMAnw4X4Nu+3/K2M+6bzC93dBuUQG1Uqp6eIoAGg== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:KCqoNPveSnwIochvhYnk2K1JEzxY7GSWlXkFzG91Gzo= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:VVAN88rIJefbj6xtJqH00A== service:fosun.sumpay.api.trade.private.agent.pay sign:G8W9DL3ddgRVMotHbmWnzWrxIvsgty444ELDpsPOi9wBAsVZIkTIgdKfRsZZKv965EpUYfmgYtdxG6rdxhOwqz68kbHJZUeKI9oSIc47GbLq7au9rBrFMc8+pN7OUlVF15oqS0URBCQzM3ZnM2BqKdTGk5shT1By7hjAZH1CauoWhRHS3nfjcIYrh5mT6U4j3hh74eHH3iJ2rZNn+R5g8F5+AhgbxuUUgOj//4LvW/CidkVdMcTIjy8xIVAA1rAue3AfbV8IXsqZe0nISM4zwd+5rmCQ+0LPtLgcOlP202zkJAC4xhKw3oAZN+/erdKQ99kqZXsGeKBe1maHNfgZ/A== sign_type:CERT terminal_type:API timestamp:20250731230250 version:1.0]"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.458]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250731230250","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.458]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250731230250","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.649]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250731230250","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250731230250\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.656]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250731230250","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:5vns6ZijQeEPA2Xhd6EinhGQPMEEcGj+G7e9W+ZtkahiQ+OQEMJzhw8ch4cnyXIie2iISXmi7k9B09ELiWlm0RojJb+2nT9cXD0lB+siKx/jUjoUYNWIeIgXNyHWKENjaAyWWuJwpTeigt+pLcYLy7QLHvQ5H4YRAC380pSY8l90Pq0tfM4pHi/+qBSmZvo1wYYVbXFrLIJHbwq0WxWuAItaPEAzzm7Y5SVN1A6AHFIYCDjFyf3rsAZZ+LhsUDvORScpfhpdyN/1AsVFey09/zm90RZOe4W1aOUy8jhSD6pjRyBaevtDcYSKUHv5mq029JrK3NKZ7EVrdMFn2E+csA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:FGSU7y7OMGrK3yHTkMNIChxK9k6WhQ5nXXcZu6EJhGA= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:hnZ11p3UtU1y12DqTVDfFw== service:fosun.sumpay.api.trade.private.agent.pay sign:C+CZF5iPUQJyRVejwK6/z9NLox8F1hqST2Cs6F2UIcKVFMCQSuqbD7tVL0QHec/W+YlmpQ2l4tUyeeCBpxyVzx8K51u5N4Cx3Fwm00Hy+hf1gAsmi7DLc1JkWvujj9zxJdzy1gipba2PwFBkmJOjSFDdj1rSfBAvELaOvlryHiktvB7k5HNg2URm3mCmYCzOzPSNqWlF4Yl0wBfGhf6P/PVtPTNQ6pGMFK8QojQOVQrd0eubUG1B3YufCm+l1yJJ3/sf8TQXpoMWPaFNQwtkPfHRjm15x501InKzVeEcw8mHk8o6xdne6g87ODLu14edHEDYVEl+es3ztRh1Zr/sZQ== sign_type:CERT terminal_type:API timestamp:20250731230250 version:1.0]"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.801]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250731230250","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.801]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250731230250","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.011]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250731230250","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250731230251\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.018]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250731230250","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:Oa1hPbH3S63PCLF9Uzzs6uhS0C0gBuqcVxU3pO6iApGWDwtbKnaxmYeynm1IyfpTRR1tiozQm2ACFIWZhBL3G0j9xeW9/j2sleQVg+C1IoK1n6p938Y4CljtpzVnNH3014wnsrChlZNGfy19TdRfWAcCdQGM0q643uiGvZw6O2GaL9dfRCf81ciYti9VhzHECwfBmQpMxSwNLx1cUgijfO3oeujA6LD+jn4apoXtv32B9acC4/HuQTf/UB7j9iXG0fdI7JM4wPyuXcjF7rgq/AR2Mhdyjx5gGO1HfqVvrqihVFvbFXKvbZ1RYx6lY6K12hsYSY5NppnGljyyvjcrOw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:vdTT9TYcFx+hRysFiWlNbfDlxqdn3p2SUQ7o+tqHuaY= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:UlidkKXmDBQBGwRiOeQ+TQ== service:fosun.sumpay.api.trade.private.agent.pay sign:XZ9MrlLXhZh6fM68/S80RTIclhsUGXkHzrUUIuTMx0RHvQugxwzDdZPXhzpwTlW7aE+zZVc7927OY5IokHX61Jr4kpnxAbm/tdvS/l4banCvw4S5m8aPITYYZihZMwqitBrZmyzlnAVrcfwLXxJeleQ3LmbIWVM5R9X0HaBUoP4qYi81SP8Fx1wukSj+E/QBuFggXrz3KGwx+7o1wg2pJ+FkLOr+xgUyNoriBkvJFj/2zYkNeXMyUTp1vogNWbT+U/tBKbZNhEyR74I9Ap+Pp1d26Gwhb/jr6YfM4iIIiQsY7xLfQWbcA+eGPmFW50UcgdCubBn6SKRap9+ktEq9FA== sign_type:CERT terminal_type:API timestamp:20250731230251 version:1.0]"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.174]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250731230250","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.174]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250731230250","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.218]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250731230320","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250731230320\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224TEST001\",\"realname\":\"林娜\",\"id_no\":\"110102200418951567\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.230]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250731230320","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:01tXqnZCoWOKWa9Q5Vv5vrGgF57PPIO07rpAF1YL1k1N0Ql1W+0O8nU/spOqZtGo0KmZSTK9XAlkzeqyE/SyTNxqhHcYCsi1JSI3kgP5aQ56OFbNsQC9TfYmnY/5rCfckpuB7FNWcuzB+K9XTTEwCyaxTXICJIj7JCWfl3aG+2Pw0RbA7QHoIgeBnfY4svcHnIMUx9IZ/0C1ph1lU3U4faolJIjukcaA14ClfNrS58HhNy2J1Gcto+lrTfl5a2V6FknSX/DjogwGWDDoktLf7DFl8VYiM7ymW1KkkHLgHpd772IyeT40VwmXqbbcS7AS5wjEwfegHgIVgBe+D3ygGQ== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:SyBCxZHa0Nxcqa0dlKNo+0luplFeTnjX7kyjfVxr+5I= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224TEST001 realname:BAHupGrr4nrQp28AOdjMGQ== service:fosun.sumpay.api.trade.private.agent.pay sign:i2jvXRETpg3mfms0U7spw6kbzbqdpBAlcL6LsMApiO+YrKDvuKOSh182fWO5MYp8gY7a9wrasSLWjNevXX5n3Re3MR01ZT+oYEJqZQvklDkH5F/X1q5EbN8KwnEW+ZbAvTPy/KPjYL5bK+z0rtnnvx+PnUKdLc0iwIXhKxdolY66uO/g7d0krSUnCkpP0FqBwFmsNaHzZ34gbgWocu9MeSEgz37zzLsCX2V03GlLu4KPGoXQzFTapj/+0C2FcogBK17dcKc9SQ8wOBQUqDURaGRUPbO41fkmidYXEH/DM7g8V2+/F0H3qL79NbxxS9jNqk+zETSXnZvOh28AYVcgZw== sign_type:CERT terminal_type:API timestamp:20250731230320 version:1.0]"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.409]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250731230320","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.409]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250731230320","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.577]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250731230320","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250731230320\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"LO20241224DEMO002\",\"realname\":\"马超\",\"id_no\":\"330102200620171789\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.584]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250731230320","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:nVfcTcp2gmH7YnnxEOmIJnRwtuZju+nyMPSNbivJtpb2zALtWYtfXrWgbn7VL2aGXfEHAousHJDU8zGtUX/95EnpWVSLcLyB5DtqHX4Rt1QFmXVQAFLC9U5UuoPhHiPbIFwQIWJl0R3cjgUCL90cM3+iRM5IpUupFhfxYCkVuCC50RpHJ6v6sOthIQfcQqA2pYrz/TG4P5CKLOPezse3ZPl7Y0ahW8HtsYiXVg7nl9azHtFTMc5ysc3CIkvr82IUwFPdULq4F9OrPfq1X29aoB9SE+vljaGS/zE8trUTaB6LWuv8U4rrrlT2L8y8FilUGWYv9+nxGspINa2syNIeZA== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:g1F+x8FIR0aXCKqo8lb0Y7YbKl6LIpuUgp2HvRoPDuc= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:LO20241224DEMO002 realname:/msAHHCxzb1yiUb4SkeKqg== service:fosun.sumpay.api.trade.private.agent.pay sign:BUllX4OVtnDd0uOe7ehatbbViGJDR9OcQLFdZKOkPM+PRYgmmx3xuzBksIGJprBig0JAgt1Ctyp1pKSM9F4pDaQMZv8kE419ezQaROTctj+CvkCpuEb5UJ9SX4Rj9ayilkcpCZH5EVLOSunLaVkdY6cXHMgJTFxK/xsv6XVCTfO+IriBABJZV5PSegs67RAArF1dKItABbmijrYNaz2/mWv4CCi1NqNpT0kxonw6P/pSkc44whc019Y58VV1Ci8koaua52aXuBMrMfdGnLnOeath+TQ5hds+uz1PQrnEbwclZmNfnLucymyahvuksgn3T5Okj4okXe7FVnnXln27wQ== sign_type:CERT terminal_type:API timestamp:20250731230320 version:1.0]"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.726]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250731230320","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.727]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250731230320","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.885]","caller":"sumpay/service.go:332","msg":"开始执行商盟支付请求","request_id":"task_auto-disbursement-compensation_20250731230320","request":"{\"service\":\"fosun.sumpay.api.trade.private.agent.pay\",\"app_id\":\"s100000040\",\"mer_no\":\"s100000040\",\"format\":\"JSON\",\"timestamp\":\"20250731230320\",\"version\":\"1.0\",\"terminal_type\":\"API\",\"order_amount\":\"5000.00\",\"order_no\":\"TEST_AUTO_001\",\"realname\":\"赵雪\",\"id_no\":\"320102200721281890\",\"card_no\":\"\",\"card_type\":\"0\",\"need_notify\":\"1\",\"notify_url\":\"http://www.baidu.com\",\"fee_amount\":\"0\",\"id_type\":\"1\",\"id_valid_date\":\"20250708-20250708\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.892]","caller":"sumpay/service.go:470","msg":"发送请求到","request_id":"task_auto-disbursement-compensation_20250731230320","url":"https://test.sumpay.cn/entrance/gateway.htm","form_data":"map[aes_key:PWVzEFpTSbAVxGP56MH/x8D83suPBafBusnTDzlbVQv4BxV5JM/J8A0HnDgCXyvQYPzdtm3Mrb/tCzipRwWnf+Z6TfneVZUfZR2fJE0L/AacSyb6qdmY3kE0XypzyX9tsf7j5NN+NdgfwXvq/+fs9q/KzOffHApzCAycECxs4rMRy7YFQfhdoc1JayRUGKfgpe04CI5pSovVOJvGJyk9ZCbj+eWnK226TbqWVPMjnf2cc9Qn1qNaWABDWoyhfAx6neEwKPa6jH2d5CuAPITnXJG+Ycol4Fr330KY2ErLNkSAp+TWG2pcEtQpkqBQMiSKkRh7kzKXshZlY7wuKwXidw== app_id:s100000040 card_type:0 fee_amount:0 format:JSON id_no:xJ9t8bPSd5RHa4SKCCW9oxcubhnUI0h6YUmIE5uyvMY= id_type:1 id_valid_date:20250708-20250708 mer_no:s100000040 need_notify:1 notify_url:aHR0cDovL3d3dy5iYWlkdS5jb20= order_amount:5000.00 order_no:TEST_AUTO_001 realname:85BG6doYC+R2u4B8Awh0nw== service:fosun.sumpay.api.trade.private.agent.pay sign:Mwijkp4hlsucIth91B9yfwdB+q1D8BdNP+kNK83VP5hlRCeWQaIOr01fiVPTVSGJ2kk2YHHlDVDvS6PyuBkzb//um265o+oDq9tlpH8/XWLkR4G/JO9pMIpd5eLv9M2cQWi2G8K+stiU+CzMYINt4cWAq5Nty+e1pX7iCY5H72waOo3Ux81ZQTT13JqtaRGa2AoMmxQeK4pzweD3LiLw4Qrr+YrPTU6NFhnh9iA8o77vZaucj+X2+682qdWN8Js0RFmmMlYjAbrixfKIxHKtjHiCRWMGf+pzrQ+7H1M0d1D1dXfuYZ2p36yqviq8mw+r1nhpvhP9+4a8UBykujVn7A== sign_type:CERT terminal_type:API timestamp:20250731230320 version:1.0]"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.033]","caller":"sumpay/service.go:480","msg":"收到响应","request_id":"task_auto-disbursement-compensation_20250731230320","status_code":200,"response_body":"{\"resp_code\":\"400002\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign_type\":\"CERT\"}"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.033]","caller":"sumpay/service.go:378","msg":"商盟支付请求执行成功","request_id":"task_auto-disbursement-compensation_20250731230320","response":"map[resp_code:400002 resp_msg:参数非法，银行卡号不能为空[AM999998] sign:Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw== sign_type:CERT]"}
