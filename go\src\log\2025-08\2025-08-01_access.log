{"level":"dev.info","ts":"[2025-08-01 11:11:03.037]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185785f2c19d1cd8c041460b","method":"POST","url":"/uniapp/user/cancelAccount","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":5.4739788,"request_size":0,"response_size":95}
{"level":"dev.info","ts":"[2025-08-01 11:14:45.727]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18578627dfe3d79caf57d62a","method":"GET","url":"/uniapp/order/get_order_bills","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0242476,"request_size":0,"response_size":189}
{"level":"dev.info","ts":"[2025-08-01 11:16:29.970]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1857863ef6cdad7ccd262ff3","method":"GET","url":"/uniapp/order/get_order_bills","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":5.0996845,"request_size":0,"response_size":189}
{"level":"dev.info","ts":"[2025-08-01 11:19:54.160]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1857866fb0686f94e3f7dfae","method":"GET","url":"/uniapp/order/get_order_bills","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0170124,"request_size":0,"response_size":2050}
{"level":"dev.info","ts":"[2025-08-01 11:20:03.002]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18578671bf928e44939096a1","method":"GET","url":"/uniapp/order/get_order_bills","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0153391,"request_size":0,"response_size":2050}
