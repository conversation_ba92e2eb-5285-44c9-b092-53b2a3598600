{"level":"dev.info","ts":"[2025-08-01 09:50:21.315]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.320]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.320]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.320]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.320]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 09:50:30.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:50:32.267]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":2.2665555,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:50:32.267]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:50:40.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:50:42.699]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":2.69894,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:50:42.699]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:50:50.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:50:52.397]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":2.3969236,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:50:52.397]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:02.090]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":2.0897141,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:51:02.090]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:10.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:12.175]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":2.1751836,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:51:12.175]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:20.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:22.947]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":2.9476473,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:51:22.947]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:30.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:32.669]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":7,"duration":2.6685531,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:51:32.669]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:40.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:43.176]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":3.1762786,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:51:43.176]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:50.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:51:54.240]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":9,"duration":4.2405825,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:51:54.240]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:03.357]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":10,"duration":3.3564639,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:52:03.357]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:10.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:14.746]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":11,"duration":4.7466179,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:52:14.746]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:20.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:21.193]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":12,"duration":1.1928679,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:52:21.194]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:30.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:32.424]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":13,"duration":2.42308,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:52:32.424]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:40.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:42.406]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":14,"duration":2.4057251,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:52:42.406]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:50.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:52:52.490]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":15,"duration":2.4897007,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:52:52.490]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:02.807]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":16,"duration":2.8071212,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:53:02.807]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:10.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:12.985]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":17,"duration":2.9846959,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:53:12.985]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:20.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:22.393]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":18,"duration":2.392392,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:53:22.393]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:30.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:32.628]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":19,"duration":2.627198,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:53:32.628]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:40.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:42.057]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":20,"duration":2.0573965,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:53:42.058]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:50.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:53:52.541]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":21,"duration":2.5416549,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:53:52.541]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:02.255]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":22,"duration":2.2546003,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:54:02.255]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:10.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:12.235]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":23,"duration":2.2357014,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:54:12.235]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:20.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:21.809]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":24,"duration":1.8096172,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:54:21.809]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:30.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:32.122]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":25,"duration":2.1220553,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:54:32.122]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:40.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:43.591]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":26,"duration":3.591145,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:54:43.591]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:50.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:54:53.290]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":27,"duration":3.2904668,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:54:53.290]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:55:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:55:05.992]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":28,"duration":5.9918775,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:55:05.993]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:55:10.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 09:55:12.388]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":29,"duration":2.3883052,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 09:55:12.388]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.584]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.587]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.589]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.589]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.589]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.589]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 10:06:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 10:06:01.257]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":1.2569254,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 10:06:01.257]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.080]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.081]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.081]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.081]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.082]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.083]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.455]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.456]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.456]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.456]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.713]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.716]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.716]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.716]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.492]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.493]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.573]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:11:03.036]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:11:03.396]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.3607407,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:11:03.396]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:12:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:12:00.276]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.2755418,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:12:00.276]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.366]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.367]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.367]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.367]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.368]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.368]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.368]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.368]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.368]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.368]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.368]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.368]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.369]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.369]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.369]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.369]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:15:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:15:00.255]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.2552284,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:15:00.255]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:16:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:16:00.284]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.2842196,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:16:00.284]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:17:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:17:00.323]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.3225624,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:17:00.323]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:18:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:18:00.269]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.2691275,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:18:00.269]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:19:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:19:00.291]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.2903849,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:19:00.291]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.060]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.063]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.063]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.063]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.063]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:20:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:20:00.287]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.2870359,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:20:00.287]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:21:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:21:00.258]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.2584513,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:21:00.258]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:22:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:22:00.295]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.2950893,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:22:00.295]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.368]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.626]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":2}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */5 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":2,"success":2,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */5 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":2,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":2}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:39:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 11:39:00.274]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.2736675,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 11:39:00.274]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.204]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":2}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */5 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":2,"success":2,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.205]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"test_long_running_task","description":"用于测试优雅退出的长时间运行任务","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":60,"retry_count":0}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */5 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"test_long_running_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":3,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":3}
{"level":"dev.info","ts":"[2025-08-01 12:05:18.206]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 12:05:20.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"test_long_running_task"}
{"level":"dev.info","ts":"[2025-08-01 12:05:20.001]","caller":"scheduler/test_graceful_shutdown.go:121","msg":"测试任务开始执行"}
{"level":"dev.info","ts":"[2025-08-01 12:05:20.001]","caller":"scheduler/test_graceful_shutdown.go:94","msg":"开始执行长时间运行的测试任务"}
{"level":"dev.info","ts":"[2025-08-01 12:05:20.001]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":1}
{"level":"dev.info","ts":"[2025-08-01 12:05:22.002]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":2}
{"level":"dev.info","ts":"[2025-08-01 12:05:24.002]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":3}
{"level":"dev.info","ts":"[2025-08-01 12:05:26.003]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":4}
{"level":"dev.info","ts":"[2025-08-01 12:05:28.004]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":5}
{"level":"dev.warn","ts":"[2025-08-01 12:05:30.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"test_long_running_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-01 12:05:30.004]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":6}
{"level":"dev.info","ts":"[2025-08-01 12:05:32.005]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":7}
{"level":"dev.info","ts":"[2025-08-01 12:05:34.005]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":8}
{"level":"dev.info","ts":"[2025-08-01 12:05:36.006]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":9}
{"level":"dev.info","ts":"[2025-08-01 12:05:38.006]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":10}
{"level":"dev.warn","ts":"[2025-08-01 12:05:40.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"test_long_running_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-01 12:05:40.006]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":11}
{"level":"dev.info","ts":"[2025-08-01 12:05:42.007]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":12}
{"level":"dev.info","ts":"[2025-08-01 12:05:44.008]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":13}
{"level":"dev.info","ts":"[2025-08-01 12:05:46.008]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":14}
{"level":"dev.info","ts":"[2025-08-01 12:05:48.009]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":15}
{"level":"dev.warn","ts":"[2025-08-01 12:05:50.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"test_long_running_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-01 12:05:50.009]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":16}
{"level":"dev.info","ts":"[2025-08-01 12:05:52.009]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":17}
{"level":"dev.info","ts":"[2025-08-01 12:05:54.010]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":18}
{"level":"dev.info","ts":"[2025-08-01 12:05:56.011]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":19}
{"level":"dev.info","ts":"[2025-08-01 12:05:58.011]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":20}
{"level":"dev.warn","ts":"[2025-08-01 12:06:00.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"test_long_running_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-01 12:06:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.error","ts":"[2025-08-01 12:06:00.002]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"auto-disbursement-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 56 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x5e\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x1e5\npanic({0x11c9960?, 0x241b500?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x132\nfincore/utils/gform.(*Engin).GetExecuteDB(0x40?)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0xe\nfincore/utils/gform.NewSession({0x1b3ff68, 0x0})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xab\nfincore/utils/gform.NewOrm(...)\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm(0x0)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x3b\nfincore/model.DB({0xc000c4f7a8, 0x1, 0xa10f5c?})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x32\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders(0x1b3cfa0?)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:182 +0x4c\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute(0xc000cdcc80, {0x1b3d048, 0xc000c00460})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68 +0x4b6\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc000bd9200, 0xc000c22180)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0xff\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc000bd9200, 0xc000c22180)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x51e\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc000bd9200, {0x1b41cf0, 0xc000cdcc80})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x245\nfincore/app/scheduler/engine.(*ScheduleEngine).registerTask.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190 +0x1f7\nreflect.Value.call({0x1136fa0?, 0xc000d0eae0?, 0x13?}, {0x135976b, 0x4}, {0x24cf220, 0x0, 0x0?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xca6\nreflect.Value.Call({0x1136fa0?, 0xc000d0eae0?, 0xc000107340?}, {0x24cf220?, 0xc000cd3b20?, 0xc000c2a000?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb9\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x1136fa0?, 0xc000d0eae0?}, {0x0, 0x0, 0x0?})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x1ea\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x1b3cfd8, 0xc000ca8550}, {0x1b3cfd8, 0xc000ca8320}, 0xc000cfef40, {0xe0, 0x4a, 0x1a, 0x9a, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0x957\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x1b3cfd8, 0xc000ca8550}, {0x1b3cfd8, 0xc000ca8320}, 0xc000cfef40, {0xe0, 0x4a, 0x1a, 0x9a, 0x95, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x6f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 55\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0x6f3\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).getPendingAutoDisbursementOrders\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:182\nfincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:68\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).registerTask.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-08-01 12:06:00.005]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0043595,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 12:06:00.005]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 12:06:00.012]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":21}
{"level":"dev.info","ts":"[2025-08-01 12:06:02.012]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":22}
{"level":"dev.info","ts":"[2025-08-01 12:06:04.013]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":23}
{"level":"dev.info","ts":"[2025-08-01 12:06:06.014]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":24}
{"level":"dev.info","ts":"[2025-08-01 12:06:08.014]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":25}
{"level":"dev.warn","ts":"[2025-08-01 12:06:10.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"test_long_running_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-01 12:06:10.015]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":26}
{"level":"dev.info","ts":"[2025-08-01 12:06:12.015]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":27}
{"level":"dev.info","ts":"[2025-08-01 12:06:14.015]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":28}
{"level":"dev.info","ts":"[2025-08-01 12:06:16.016]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":29}
{"level":"dev.info","ts":"[2025-08-01 12:06:18.016]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":30}
{"level":"dev.warn","ts":"[2025-08-01 12:06:20.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"test_long_running_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-01 12:06:20.017]","caller":"scheduler/test_graceful_shutdown.go:114","msg":"长时间运行的测试任务执行完成"}
{"level":"dev.info","ts":"[2025-08-01 12:06:20.017]","caller":"scheduler/test_graceful_shutdown.go:127","msg":"测试任务执行完成"}
{"level":"dev.info","ts":"[2025-08-01 12:06:20.018]","caller":"scheduler/test_graceful_shutdown.go:133","msg":"测试任务执行成功"}
{"level":"dev.info","ts":"[2025-08-01 12:06:20.018]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"test_long_running_task","job_id":1,"duration":60.0169899,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 12:06:20.019]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"test_long_running_task"}
{"level":"dev.info","ts":"[2025-08-01 12:06:30.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"test_long_running_task"}
{"level":"dev.info","ts":"[2025-08-01 12:06:30.000]","caller":"scheduler/test_graceful_shutdown.go:121","msg":"测试任务开始执行"}
{"level":"dev.info","ts":"[2025-08-01 12:06:30.000]","caller":"scheduler/test_graceful_shutdown.go:94","msg":"开始执行长时间运行的测试任务"}
{"level":"dev.info","ts":"[2025-08-01 12:06:30.001]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":1}
{"level":"dev.info","ts":"[2025-08-01 12:06:30.290]","caller":"scheduler/scheduler.go:78","msg":"正在停止调度器"}
{"level":"dev.info","ts":"[2025-08-01 12:06:30.290]","caller":"scheduler/manager.go:123","msg":"正在停止调度器管理器","graceful_shutdown_enabled":true,"wait_for_tasks_timeout":0.000000015}
{"level":"dev.info","ts":"[2025-08-01 12:06:30.291]","caller":"engine/scheduler.go:100","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 12:06:32.001]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":2}
{"level":"dev.info","ts":"[2025-08-01 12:06:34.002]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":3}
{"level":"dev.info","ts":"[2025-08-01 12:06:36.003]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":4}
{"level":"dev.info","ts":"[2025-08-01 12:06:38.004]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":5}
{"level":"dev.info","ts":"[2025-08-01 12:06:40.005]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":6}
{"level":"dev.error","ts":"[2025-08-01 12:06:40.291]","caller":"engine/scheduler.go:104","msg":"停止调度器失败","error":"gocron: timed out waiting for jobs to finish","stacktrace":"fincore/app/scheduler/engine.(*ScheduleEngine).Stop\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:104\nfincore/app/scheduler.(*SchedulerManager).Stop\n\tD:/work/code/fincore/go/src/app/scheduler/manager.go:132\nfincore/app/scheduler.Stop\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:80\nfincore/app/scheduler.StopAndCleanup\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:251\nfincore/app/scheduler.TestGracefulShutdown\n\tD:/work/code/fincore/go/src/app/scheduler/test_graceful_shutdown.go:51\nmain.main\n\tD:/work/code/fincore/go/src/test_graceful_main.go:27\nruntime.main\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/proc.go:283"}
{"level":"dev.error","ts":"[2025-08-01 12:06:40.291]","caller":"scheduler/manager.go:133","msg":"停止调度引擎失败","error":"gocron: timed out waiting for jobs to finish","stacktrace":"fincore/app/scheduler.(*SchedulerManager).Stop\n\tD:/work/code/fincore/go/src/app/scheduler/manager.go:133\nfincore/app/scheduler.Stop\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:80\nfincore/app/scheduler.StopAndCleanup\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:251\nfincore/app/scheduler.TestGracefulShutdown\n\tD:/work/code/fincore/go/src/app/scheduler/test_graceful_shutdown.go:51\nmain.main\n\tD:/work/code/fincore/go/src/test_graceful_main.go:27\nruntime.main\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/proc.go:283"}
{"level":"dev.error","ts":"[2025-08-01 12:06:40.291]","caller":"scheduler/scheduler.go:81","msg":"停止调度器失败","error":"gocron: timed out waiting for jobs to finish","stacktrace":"fincore/app/scheduler.Stop\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:81\nfincore/app/scheduler.StopAndCleanup\n\tD:/work/code/fincore/go/src/app/scheduler/scheduler.go:251\nfincore/app/scheduler.TestGracefulShutdown\n\tD:/work/code/fincore/go/src/app/scheduler/test_graceful_shutdown.go:51\nmain.main\n\tD:/work/code/fincore/go/src/test_graceful_main.go:27\nruntime.main\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/proc.go:283"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.328]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":2}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */5 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":2,"success":2,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.329]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"test_long_running_task","description":"用于测试优雅退出的长时间运行任务","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":60,"retry_count":0}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.330]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.330]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.330]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.330]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.330]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */5 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.330]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"test_long_running_task","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.330]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":3,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.330]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":3}
{"level":"dev.info","ts":"[2025-08-01 12:07:26.331]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 12:07:30.001]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"test_long_running_task"}
{"level":"dev.info","ts":"[2025-08-01 12:07:30.001]","caller":"scheduler/test_graceful_shutdown.go:121","msg":"测试任务开始执行"}
{"level":"dev.info","ts":"[2025-08-01 12:07:30.001]","caller":"scheduler/test_graceful_shutdown.go:94","msg":"开始执行长时间运行的测试任务"}
{"level":"dev.info","ts":"[2025-08-01 12:07:30.001]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":1}
{"level":"dev.info","ts":"[2025-08-01 12:07:32.002]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":2}
{"level":"dev.info","ts":"[2025-08-01 12:07:34.002]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":3}
{"level":"dev.info","ts":"[2025-08-01 12:07:36.003]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":4}
{"level":"dev.info","ts":"[2025-08-01 12:07:38.003]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":5}
{"level":"dev.warn","ts":"[2025-08-01 12:07:40.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"test_long_running_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-01 12:07:40.004]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":6}
{"level":"dev.info","ts":"[2025-08-01 12:07:42.004]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":7}
{"level":"dev.info","ts":"[2025-08-01 12:07:44.005]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":8}
{"level":"dev.info","ts":"[2025-08-01 12:07:46.006]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":9}
{"level":"dev.info","ts":"[2025-08-01 12:07:48.006]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":10}
{"level":"dev.warn","ts":"[2025-08-01 12:07:50.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"test_long_running_task","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-08-01 12:07:50.007]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":11}
{"level":"dev.info","ts":"[2025-08-01 12:07:52.007]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":12}
{"level":"dev.info","ts":"[2025-08-01 12:07:54.008]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":13}
{"level":"dev.info","ts":"[2025-08-01 12:07:56.008]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":14}
{"level":"dev.info","ts":"[2025-08-01 12:07:56.335]","caller":"scheduler/scheduler.go:78","msg":"正在停止调度器"}
{"level":"dev.info","ts":"[2025-08-01 12:07:56.336]","caller":"scheduler/manager.go:123","msg":"正在停止调度器管理器","graceful_shutdown_enabled":true,"wait_for_tasks_timeout":15}
{"level":"dev.info","ts":"[2025-08-01 12:07:56.336]","caller":"engine/scheduler.go:100","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 12:07:58.009]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":15}
{"level":"dev.info","ts":"[2025-08-01 12:08:00.009]","caller":"scheduler/test_graceful_shutdown.go:109","msg":"任务执行中...","step":16}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.919]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.919]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.919]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.919]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":2}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */5 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":2,"success":2,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.920]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.921]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */5 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.921]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":2,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.921]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":2}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.921]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 12:10:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 12:10:00.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.warn","ts":"[2025-08-01 12:10:00.026]","caller":"engine/executor.go:166","msg":"任务执行失败，准备重试","task_name":"disbursement-status-sync-compensation","attempt":0,"max_retries":3,"error":"查询待同步状态的订单失败: 查询数据库失败: Error 1267 (HY000): Illegal mix of collations (utf8mb4_0900_ai_ci,IMPLICIT) and (utf8mb4_general_ci,IMPLICIT) for operation '='"}
{"level":"dev.info","ts":"[2025-08-01 12:10:00.026]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0254054,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-01 12:10:00.026]","caller":"engine/executor.go:128","msg":"等待重试","task_name":"disbursement-status-sync-compensation","attempt":1,"retry_interval":30}
{"level":"dev.info","ts":"[2025-08-01 12:10:00.026]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.104]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.105]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.105]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.105]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":2}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */5 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":2,"success":2,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.107]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.108]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */5 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.108]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":2,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.108]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":2}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.108]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.974]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.975]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.975]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.975]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":2}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */5 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":2,"success":2,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */5 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.976]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":2,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.977]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":2}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.977]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.703]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":2}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */5 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":2,"success":2,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.704]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.705]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.705]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */5 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.705]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":2,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.705]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":2}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.705]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.787]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":2}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */5 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":2,"success":2,"failure":0}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.788]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.789]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.789]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */5 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.789]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":2,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.789]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":2}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.789]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
