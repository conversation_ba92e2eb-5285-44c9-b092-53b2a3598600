{"level":"dev.info","ts":"[2025-07-31 23:02:50.000]","caller":"orders/auto_disbursement_compensation.go:200","msg":"自动放款补偿任务即将开始","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","operation":"task_starting"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.001]","caller":"orders/auto_disbursement_compensation.go:61","msg":"开始执行自动放款补偿任务","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.037]","caller":"orders/auto_disbursement_compensation.go:89","msg":"找到需要补偿放款的订单","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","operation":"found_orders","order_count":5}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.038]","caller":"orders/auto_disbursement_compensation.go:116","msg":"开始处理订单自动放款","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"LO20241224TEST001","order_id":1,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.038]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_14","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.038]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20241224TEST001","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.038]","caller":"order/management_service.go:67","msg":"开始处理订单放款流程","request_id":"task_auto-disbursement-compensation_20250731230250","order_no":"LO20241224TEST001","operator_id":0,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.494]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20241224TEST001","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.494]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20241224TEST001","action":"unlock_success","is_locked":false}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.494]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_14","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.494]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_14","action":"unlock_success","is_locked":false}
{"level":"dev.error","ts":"[2025-07-31 23:02:50.494]","caller":"orders/auto_disbursement_compensation.go:128","msg":"订单自动放款失败","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"LO20241224TEST001","order_id":1,"operation":"disbursement_failed","error":"系统自动失败, 失败原因: 参数非法，银行卡号不能为空[AM999998]","stacktrace":"fincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:128\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.495]","caller":"orders/auto_disbursement_compensation.go:116","msg":"开始处理订单自动放款","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"LO20241224DEMO002","order_id":3,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.495]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_16","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.495]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20241224DEMO002","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.495]","caller":"order/management_service.go:67","msg":"开始处理订单放款流程","request_id":"task_auto-disbursement-compensation_20250731230250","order_no":"LO20241224DEMO002","operator_id":0,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.857]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20241224DEMO002","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.857]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20241224DEMO002","action":"unlock_success","is_locked":false}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.857]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_16","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.857]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_16","action":"unlock_success","is_locked":false}
{"level":"dev.error","ts":"[2025-07-31 23:02:50.857]","caller":"orders/auto_disbursement_compensation.go:128","msg":"订单自动放款失败","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"LO20241224DEMO002","order_id":3,"operation":"disbursement_failed","error":"系统自动失败, 失败原因: 参数非法，银行卡号不能为空[AM999998]","stacktrace":"fincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:128\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.858]","caller":"orders/auto_disbursement_compensation.go:116","msg":"开始处理订单自动放款","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"TEST_AUTO_001","order_id":5,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.858]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_17","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.858]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_TEST_AUTO_001","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.858]","caller":"order/management_service.go:67","msg":"开始处理订单放款流程","request_id":"task_auto-disbursement-compensation_20250731230250","order_no":"TEST_AUTO_001","operator_id":0,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.211]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_TEST_AUTO_001","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.211]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_TEST_AUTO_001","action":"unlock_success","is_locked":false}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.211]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_17","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.211]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_17","action":"unlock_success","is_locked":false}
{"level":"dev.error","ts":"[2025-07-31 23:02:51.211]","caller":"orders/auto_disbursement_compensation.go:128","msg":"订单自动放款失败","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"TEST_AUTO_001","order_id":5,"operation":"disbursement_failed","error":"系统自动失败, 失败原因: 参数非法，银行卡号不能为空[AM999998]","stacktrace":"fincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:128\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.211]","caller":"orders/auto_disbursement_compensation.go:116","msg":"开始处理订单自动放款","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"LO20250730DHEAQTP5","order_id":44,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.211]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_92","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.211]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20250730DHEAQTP5","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.211]","caller":"order/management_service.go:67","msg":"开始处理订单放款流程","request_id":"task_auto-disbursement-compensation_20250731230250","order_no":"LO20250730DHEAQTP5","operator_id":0,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.245]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20250730DHEAQTP5","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.245]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20250730DHEAQTP5","action":"unlock_success","is_locked":false}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.245]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_92","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.245]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_92","action":"unlock_success","is_locked":false}
{"level":"dev.error","ts":"[2025-07-31 23:02:51.245]","caller":"orders/auto_disbursement_compensation.go:128","msg":"订单自动放款失败","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"LO20250730DHEAQTP5","order_id":44,"operation":"disbursement_failed","error":"获取用户信息或银行卡信息失败: 业务应用账户不存在","stacktrace":"fincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:128\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.246]","caller":"orders/auto_disbursement_compensation.go:116","msg":"开始处理订单自动放款","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"LO20250731CVJJPHOX","order_id":114,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.366]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_108","action":"lock_success","duration":25.1203339,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.366]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20250731CVJJPHOX","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.366]","caller":"order/management_service.go:67","msg":"开始处理订单放款流程","request_id":"task_auto-disbursement-compensation_20250731230250","order_no":"LO20250731CVJJPHOX","operator_id":0,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.381]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20250731CVJJPHOX","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.382]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_disbursement_lock_LO20250731CVJJPHOX","action":"unlock_success","is_locked":false}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.382]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_108","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.382]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","key":"order_creation_lock_108","action":"unlock_success","is_locked":false}
{"level":"dev.error","ts":"[2025-07-31 23:03:16.382]","caller":"orders/auto_disbursement_compensation.go:128","msg":"订单自动放款失败","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","order_no":"LO20250731CVJJPHOX","order_id":114,"operation":"disbursement_failed","error":"订单状态不允许放款，当前状态: 交易关闭","stacktrace":"fincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:128\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.382]","caller":"orders/auto_disbursement_compensation.go:151","msg":"自动放款补偿任务执行完成","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","operation":"execution_completed","total_orders":5,"processed_count":5,"success_count":0,"failure_count":5,"duration":"26.3802465s"}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.382]","caller":"orders/auto_disbursement_compensation.go:228","msg":"自动放款补偿任务执行完成","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","operation":"task_complete"}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.382]","caller":"orders/auto_disbursement_compensation.go:209","msg":"自动放款补偿任务执行成功","request_id":"task_auto-disbursement-compensation_20250731230250","task":"auto-disbursement-compensation","operation":"task_success"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.000]","caller":"orders/auto_disbursement_compensation.go:200","msg":"自动放款补偿任务即将开始","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","operation":"task_starting"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.000]","caller":"orders/auto_disbursement_compensation.go:61","msg":"开始执行自动放款补偿任务","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","operation":"start_execution"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.027]","caller":"orders/auto_disbursement_compensation.go:89","msg":"找到需要补偿放款的订单","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","operation":"found_orders","order_count":4}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.027]","caller":"orders/auto_disbursement_compensation.go:116","msg":"开始处理订单自动放款","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","order_no":"LO20241224TEST001","order_id":1,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.027]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_14","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.027]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_LO20241224TEST001","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.027]","caller":"order/management_service.go:67","msg":"开始处理订单放款流程","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","order_no":"LO20241224TEST001","operator_id":0,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.434]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_LO20241224TEST001","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.434]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_LO20241224TEST001","action":"unlock_success","is_locked":false}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.434]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_14","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.434]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_14","action":"unlock_success","is_locked":false}
{"level":"dev.error","ts":"[2025-07-31 23:03:20.434]","caller":"orders/auto_disbursement_compensation.go:128","msg":"订单自动放款失败","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","order_no":"LO20241224TEST001","order_id":1,"operation":"disbursement_failed","error":"系统自动失败, 失败原因: 参数非法，银行卡号不能为空[AM999998]","stacktrace":"fincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:128\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.434]","caller":"orders/auto_disbursement_compensation.go:116","msg":"开始处理订单自动放款","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","order_no":"LO20241224DEMO002","order_id":3,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.434]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_16","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.434]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_LO20241224DEMO002","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.434]","caller":"order/management_service.go:67","msg":"开始处理订单放款流程","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","order_no":"LO20241224DEMO002","operator_id":0,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.747]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_LO20241224DEMO002","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.747]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_LO20241224DEMO002","action":"unlock_success","is_locked":false}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.747]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_16","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.747]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_16","action":"unlock_success","is_locked":false}
{"level":"dev.error","ts":"[2025-07-31 23:03:20.747]","caller":"orders/auto_disbursement_compensation.go:128","msg":"订单自动放款失败","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","order_no":"LO20241224DEMO002","order_id":3,"operation":"disbursement_failed","error":"系统自动失败, 失败原因: 参数非法，银行卡号不能为空[AM999998]","stacktrace":"fincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:128\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.747]","caller":"orders/auto_disbursement_compensation.go:116","msg":"开始处理订单自动放款","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","order_no":"TEST_AUTO_001","order_id":5,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.747]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_17","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.747]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_TEST_AUTO_001","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.747]","caller":"order/management_service.go:67","msg":"开始处理订单放款流程","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","order_no":"TEST_AUTO_001","operator_id":0,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.053]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_TEST_AUTO_001","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.053]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_TEST_AUTO_001","action":"unlock_success","is_locked":false}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.053]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_17","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.053]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_17","action":"unlock_success","is_locked":false}
{"level":"dev.error","ts":"[2025-07-31 23:03:21.053]","caller":"orders/auto_disbursement_compensation.go:128","msg":"订单自动放款失败","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","order_no":"TEST_AUTO_001","order_id":5,"operation":"disbursement_failed","error":"系统自动失败, 失败原因: 参数非法，银行卡号不能为空[AM999998]","stacktrace":"fincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:128\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.053]","caller":"orders/auto_disbursement_compensation.go:116","msg":"开始处理订单自动放款","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","order_no":"LO20250730DHEAQTP5","order_id":44,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.053]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_92","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.053]","caller":"lock/simple_lock.go:266","msg":"加锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_LO20250730DHEAQTP5","action":"lock_success","duration":0,"is_locked":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.053]","caller":"order/management_service.go:67","msg":"开始处理订单放款流程","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","order_no":"LO20250730DHEAQTP5","operator_id":0,"operation":"process_disbursement"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_LO20250730DHEAQTP5","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_disbursement_lock_LO20250730DHEAQTP5","action":"unlock_success","is_locked":false}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"lock/simple_lock.go:280","msg":"开始解锁","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_92","action":"unlock"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"lock/simple_lock.go:292","msg":"解锁成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","key":"order_creation_lock_92","action":"unlock_success","is_locked":false}
{"level":"dev.error","ts":"[2025-07-31 23:03:21.088]","caller":"orders/auto_disbursement_compensation.go:128","msg":"订单自动放款失败","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","order_no":"LO20250730DHEAQTP5","order_id":44,"operation":"disbursement_failed","error":"获取用户信息或银行卡信息失败: 业务应用账户不存在","stacktrace":"fincore/app/scheduler/tasks/orders.(*AutoDisbursementCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/auto_disbursement_compensation.go:128\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:190\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"orders/auto_disbursement_compensation.go:151","msg":"自动放款补偿任务执行完成","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","operation":"execution_completed","total_orders":4,"processed_count":4,"success_count":0,"failure_count":4,"duration":"1.0880419s"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"orders/auto_disbursement_compensation.go:228","msg":"自动放款补偿任务执行完成","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","operation":"task_complete"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"orders/auto_disbursement_compensation.go:209","msg":"自动放款补偿任务执行成功","request_id":"task_auto-disbursement-compensation_20250731230250","request_id":"task_auto-disbursement-compensation_20250731230320","task":"auto-disbursement-compensation","operation":"task_success"}
