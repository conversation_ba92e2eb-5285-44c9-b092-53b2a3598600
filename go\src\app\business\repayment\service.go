package repayment

import (
	"context"
	"encoding/json"
	"errors"
	"fincore/app/dianziqian/utils"
	"fincore/global"
	"fincore/model"
	"fincore/route/middleware"
	"fincore/thirdparty/payment/sumpay"
	"fincore/utils/gform"
	"fincore/utils/lock"
	"fincore/utils/log"
	"fmt"
	"math"
	"math/rand"
	"net"
	"time"

	"github.com/gin-gonic/gin"
)

// 支付相关常量
const (
	PaymentCurrency  = "CNY"   // 币种
	PaymentGoodsName = "互金产品"  // 商品名称
	PaymentTradeCode = "T0002" // 交易码
	PaymentGoodsNum  = 1       // 商品数量
	PaymentGoodsType = 1       // 商品类型：虚拟物品
)

// 代扣类型常量
const (
	WithholdTypeAsset     = "ASSET"     // 资产代扣
	WithholdTypeGuarantee = "GUARANTEE" // 担保代扣
)

// 代付回调状态常量
const (
	DisbursementCallbackStatusSuccess    = "00" // 成功
	DisbursementCallbackStatusProcessing = "01" // 处理中
	DisbursementCallbackStatusFailed     = "03" // 失败
)

// 代付回调响应码常量
const (
	CallbackResponseCodeSuccess = "000000" // 成功
	CallbackResponseCodeFailed  = "000001" // 失败
)

// 回调基础结构体
type CallbackBase struct {
	RespCode string `json:"resp_code"` // 响应码 成功返回 000000
	RespMsg  string `json:"resp_msg"`  // 响应码不为 0 返回失败原因
	SignType string `json:"sign_type"` // 签名类型 支持CERT和RSA必须大写,API接入方式使用CERT
	Sign     string `json:"sign"`      // 签名
}

// 代付回调通知
type DisbursementCallbackRequest struct {
	CallbackBase
	OrderNo       string `json:"order_no"`                        // 订单号
	TraceNo       string `json:"trace_no"`                        // 交易流水号
	OrderAmount   string `json:"order_amount"`                    // 订单金额 保留两位小数
	Status        string `json:"status"`                          // 状态 00 成功，01 处理中，03 失败
	FeeAmount     string `optional:"true" json:"fee"`             // 手续费 保留两位小数
	OboType       string `optional:"true" json:"obo_type"`        // 业务类型 01 代付，02 代收
	Remark        string `optional:"true" json:"remark"`          // 商户请求传入的remark, 原样返回
	TtfReturnCode string `optional:"true" json:"ttf_return_code"` // 失败时可能会返回
	TtfReturnMsg  string `optional:"true" json:"ttf_return_msg"`  // 失败时可能会返回
}

type DisbursementCallbackResponse struct {
	RespCode string `json:"resp_code"` // 响应码 成功返回 000000
	RespMsg  string `json:"resp_msg"`  // 响应码不为 0 返回失败原因
}

type PaymentCallbackRequest struct {
	CallbackBase
	MerNo           string  `json:"mer_no"`                            // 商户号
	OrderNo         string  `json:"order_no"`                          // 订单号
	OffsetAmount    float64 `json:"offset_amount"`                     // 优惠金额
	PaidAmout       float64 `json:"paid_amount"`                       // 实付金额
	TradeNo         string  `json:"trade_no"`                          // 交易流水号
	OrderTime       string  `json:"order_time"`                        // 订单时间
	Status          string  `json:"status"`                            // 状态 00 成功，01 处理中，03 失败
	SuccessTime     string  `json:"success_time"`                      // 成功时间
	SuccessAmount   string  `json:"success_amount"`                    // 成功金额
	Remark          string  `json:"remark" optional:"true"`            // 备注
	TtfReturnCode   string  `json:"ttf_return_code" optional:"true"`   // 失败时可能会返回
	TtfReturnMsg    string  `json:"ttf_return_msg" optional:"true"`    // 失败时可能会返回
	TraceCode       string  `json:"trace_code" optional:"true"`        // T0001:担保交易 T0002:即时交易
	PayProductCode  string  `json:"pay_product_code" optional:"true"`  // 支付产品码
	OffestDetail    string  `json:"offest_detail" optional:"true"`     // 优惠详情
	ChannelSerialNo string  `json:"channel_serial_no" optional:"true"` // 渠道流水号
	PeriodNum       string  `json:"period_num" optional:"true"`        // 信用卡分期支付必填，暂只有：3、6、9、12、18、24、36、60
	Attach          string  `json:"attach" optional:"true"`            // 附加信息
}

// PaymentService 支付服务
type PaymentService struct {
	billModel        *model.BusinessRepaymentBillsService
	transactionModel *model.BusinessPaymentTransactionsService
	bankCardModel    *model.BusinessBankCardsService
	orderModel       *model.BusinessLoanOrdersService
	operationModel   *model.BusinessOrderOperationLogsService
	ctx              context.Context
	logger           *log.Logger
}

func WithLogger(logger *log.Logger) func(*PaymentService) {
	return func(service *PaymentService) {
		service.logger = logger
	}
}

// NewPaymentService 创建支付服务实例
func NewPaymentService(ctx context.Context, opts ...func(*PaymentService)) *PaymentService {
	service := &PaymentService{
		billModel:        model.NewBusinessRepaymentBillsService(ctx),
		transactionModel: model.NewBusinessPaymentTransactionsService(ctx),
		bankCardModel:    model.NewBusinessBankCardsService(ctx),
		orderModel:       model.NewBusinessLoanOrdersService(ctx),
		operationModel:   model.NewBusinessOrderOperationLogsService(),
		ctx:              ctx,
		logger:           log.Payment().WithContext(ctx),
	}
	for _, opt := range opts {
		opt(service)
	}
	return service
}

// HandlePaymentCallback 处理支付回调通知
func (s *PaymentService) HandlePaymentCallback(req *PaymentCallbackRequest) (err error) {
	// 🔒 添加回调处理锁，防止并发回调
	lockKey := fmt.Sprintf("payment_callback:%s", req.OrderNo)
	callbackLock := lock.GetLock(lockKey, 30*time.Second)

	callbackLock.Lock()
	defer callbackLock.Unlock()

	// 分解 order_no 获取订单编号
	orderNo, transactionNo, withholdType, err := ParsePayOrderNoFields(req.OrderNo)
	if err != nil {
		return err
	}

	// 根据流水号和订单编号查询交易记录
	transaction, err := s.transactionModel.GetTransactionByNo(transactionNo)
	if err != nil {
		return err
	}

	if transaction.OrderNo != orderNo {
		return fmt.Errorf("订单编号不匹配")
	}

	if *transaction.WithholdType != withholdType {
		return fmt.Errorf("代扣类型与流水记录不匹配")
	}

	callbackResult, err := utils.StructToJson(req)
	if err != nil {
		return fmt.Errorf("转换结果失败: %v", err)
	}

	// 根据回调状态调用不同的处理函数
	switch req.Status {
	case DisbursementCallbackStatusSuccess:
		err = s.handlePaymentSuccess(transaction, req, withholdType, callbackResult)
	case DisbursementCallbackStatusFailed:
		err = s.handlePaymentFailed(transaction, req, callbackResult)
	case DisbursementCallbackStatusProcessing:
		err = s.handlePaymentProcessing(transaction, req, callbackResult)
	default:
		err = fmt.Errorf("未知的回调状态: %s", req.Status)
	}
	return
}

// handlePaymentSuccess 处理支付成功回调
func (s *PaymentService) handlePaymentSuccess(transaction *model.BusinessPaymentTransactions, req *PaymentCallbackRequest, withholdType string, callbackResult string) error {
	return model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		// 更新支付流水
		updateMap := map[string]interface{}{
			"status":                 model.TransactionStatusSuccess,
			"amount":                 model.Decimal(req.PaidAmout),
			"channel_transaction_no": req.ChannelSerialNo,
			"error_code":             req.RespCode,
			"error_message":          req.RespMsg,
			"callback_result":        callbackResult,
		}
		updateErr := s.transactionModel.UpdateTransactionStatus(tx, model.UpdateTransactionStatusResultWhere{
			ID: transaction.ID,
		}, updateMap)
		if updateErr != nil {
			return fmt.Errorf("更新支付流水失败: %v", updateErr)
		}

		// 更新账单
		billID := transaction.BillID
		if billID == nil {
			return fmt.Errorf("账单ID为空")
		}

		// 获取账单
		bill, err := s.billModel.GetBillByID(*billID)
		if err != nil {
			return fmt.Errorf("获取账单失败: %v", err)
		}

		// 当期已还金额累加
		updateBillMap := map[string]interface{}{
			"paid_amount": bill.PaidAmount + model.Decimal(req.PaidAmout),
		}
		// 支付资管费，对应账单的资管费累减
		if withholdType == "ASSET" {
			updateBillMap["asset_management_entry"] = bill.AssetManagementEntry - model.Decimal(req.PaidAmout)
		} else {
			// 支付担保费，对应账单的担保费累减
			updateBillMap["due_guarantee_fee"] = bill.DueGuaranteeFee - model.Decimal(req.PaidAmout)
		}

		// 是否还清
		if updateBillMap["paid_amount"].(model.Decimal) >= updateBillMap["total_due_amount"].(model.Decimal) {
			now := time.Now()
			updateBillMap["paid_at"] = now
			updateBillMap["status"] = model.RepaymentBillStatusSettled
		} else {
			updateBillMap["status"] = model.RepaymentBillStatusPartialPaid
		}

		_, updateBillErr := s.billModel.UpdateBill(tx, model.UpdateBillResultWhere{
			ID: *billID,
		}, updateBillMap)
		if updateBillErr != nil {
			return fmt.Errorf("更新账单失败: %v", updateBillErr)
		}

		// 获取订单
		order, err := s.orderModel.GetOrderByID(transaction.OrderID)
		if err != nil {
			return fmt.Errorf("获取订单失败: %v", err)
		}

		// 更新订单已还金额
		amountPaid := order.AmountPaid + model.Decimal(req.PaidAmout)

		orderUpdateMap := map[string]interface{}{
			"amount_paid": amountPaid,
		}

		// 检查并更新订单状态
		if err := s.checkAndUpdateOrderStatus(tx, transaction.OrderID, amountPaid, order.TotalRepayableAmount, orderUpdateMap); err != nil {
			return err
		}

		orderUpdateErr := s.orderModel.UpdateOrder(tx, model.UpdateOrderCondition{
			ID: transaction.OrderID,
		}, orderUpdateMap)
		if orderUpdateErr != nil {
			return fmt.Errorf("更新订单已还金额失败: %v", orderUpdateErr)
		}

		return nil
	})
}

// handlePaymentFailed 处理支付失败回调
func (s *PaymentService) handlePaymentFailed(transaction *model.BusinessPaymentTransactions, req *PaymentCallbackRequest, callbackResult string) error {
	updateMap := map[string]interface{}{
		"status":          model.TransactionStatusFailed,
		"error_code":      req.RespCode,
		"error_message":   req.RespMsg,
		"callback_result": callbackResult,
	}

	updateTransactionErr := s.transactionModel.UpdateTransactionStatus(nil, model.UpdateTransactionStatusResultWhere{
		ID: transaction.ID,
	}, updateMap)

	if updateTransactionErr != nil {
		return fmt.Errorf("更新流水状态失败: %v", updateTransactionErr)
	}

	return nil
}

// QueryPaymentStatus 查询第三方支付状态并更新本地流水
func (s *PaymentService) QueryPaymentStatus(transactionNo string) (*QueryPaymentStatusResponse, error) {
	// 1. 查询本地交易记录
	transaction, err := s.transactionModel.GetTransactionByNo(transactionNo)
	if err != nil {
		return nil, fmt.Errorf("查询交易记录失败: %v", err)
	}

	// 2. 如果不是已提交状态的交易 还是返回流水的状态
	if transaction.Status != model.TransactionStatusSubmitted {
		return &QueryPaymentStatusResponse{
			Status: getStatusString(transaction.Status),
		}, nil
	}

	// 3. 检查第三方订单号
	if transaction.ThirdPartyOrderNo == "" {
		return nil, fmt.Errorf("第三方订单号为空，无法查询")
	}

	// 4. 调用第三方支付查询接口
	paymentService, err := sumpay.NewSumpayService()
	if err != nil {
		return nil, fmt.Errorf("获取支付服务失败: %v", err)
	}

	// 5. 使用QueryPayOrder接口查询订单状态
	queryRequest := &sumpay.QueryPayOrderRequest{
		BaseRequest: sumpay.NewBaseRequest("fosun.sumpay.api.trade.order.search.merchant",
			global.App.Config.SumPay.AppId, global.App.Config.SumPay.MerNo),
		OrderNo: transaction.ThirdPartyOrderNo,
	}

	queryResponse, err := paymentService.QueryPayOrder(queryRequest)
	if err != nil {
		return nil, fmt.Errorf("查询第三方支付状态失败: %v", err)
	}
	// 6. 检查查询是否成功
	if !queryResponse.IsSuccess() {
		// 查询失败，记录错误但不更新交易状态
		return nil, fmt.Errorf("第三方查询失败: %s - %s", queryResponse.RespCode, queryResponse.RespMsg)
	}

	// 7. 解析查询响应数据
	businessResp, err := queryResponse.GetQueryPayOrderResponseData()
	if err != nil {
		return nil, fmt.Errorf("解析查询响应数据失败: %v", err)
	}

	// 8. 根据查询结果更新本地状态
	var updateStatus int
	var errorCode, errorMessage string

	switch businessResp.Status {
	case "1": // 成功
		updateStatus = model.TransactionStatusSuccess
		if transaction.BillID != nil {
			err = s.updateBillAndOrderPaidAmount(*transaction.BillID, float64(transaction.Amount))
			if err != nil {
				return nil, fmt.Errorf("更新账单和订单已还金额失败: %v", err)
			}
		}

	case "0": // 失败
		updateStatus = model.TransactionStatusFailed
		errorCode = "PAYMENT_FAILED"
		errorMessage = "支付失败"

	case "2": // 处理中
		updateStatus = model.TransactionStatusSubmitted // 保持已提交状态

	default:
		updateStatus = model.TransactionStatusFailed
		errorCode = "UNKNOWN_STATUS"
		errorMessage = fmt.Sprintf("未知的支付状态: %s", businessResp.Status)
	}

	// 9. 更新本地交易状态
	updateMap := map[string]interface{}{
		"status": updateStatus,
	}
	if errorCode != "" {
		updateMap["error_code"] = errorCode
	}
	if errorMessage != "" {
		updateMap["error_message"] = errorMessage
	}

	err = s.transactionModel.UpdateTransactionStatus(
		nil,
		model.UpdateTransactionStatusResultWhere{
			TransactionNo: transactionNo,
		},
		updateMap,
	)
	if err != nil {
		return nil, fmt.Errorf("更新交易状态失败: %v", err)
	}

	// 10. 重新查询更新后的交易记录
	updatedTransaction, err := s.transactionModel.GetTransactionByNo(transactionNo)
	if err != nil {
		return nil, fmt.Errorf("查询更新后的交易记录失败: %v", err)
	}

	// 11. 构造响应
	response := &QueryPaymentStatusResponse{
		TransactionNo:        updatedTransaction.TransactionNo,
		Status:               getStatusString(updatedTransaction.Status),
		Amount:               float64(updatedTransaction.Amount),
		ChannelTransactionNo: updatedTransaction.ChannelTransactionNo,
		UpdatedAt:            updatedTransaction.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	if updatedTransaction.ErrorCode != "" {
		response.ErrorCode = updatedTransaction.ErrorCode
	}
	if updatedTransaction.ErrorMessage != "" {
		response.ErrorMessage = updatedTransaction.ErrorMessage
	}

	return response, nil
}

// updateBillAndOrderPaidAmount 根据账单ID更新还款账单和订单的已还金额
func (s *PaymentService) updateBillAndOrderPaidAmount(billID int, paidAmount float64) error {
	// 1. 根据账单ID查询账单
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return fmt.Errorf("查询还款账单失败: %v", err)
	}
	if bill == nil {
		return fmt.Errorf("账单不存在")
	}

	// 2. 查询订单信息
	order, err := s.orderModel.GetOrderByID(bill.OrderID)
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	// 3. 计算账单有效还款金额（已还金额 + 本次支付金额 + 减免金额）
	billEffectivePayment := bill.PaidAmount + model.Decimal(paidAmount) + bill.TotalWaiveAmount

	// 计算账单新的已还金额（不包含减免金额）
	newBillPaidAmount := bill.PaidAmount + model.Decimal(paidAmount)
	if newBillPaidAmount > bill.TotalDueAmount {
		newBillPaidAmount = bill.TotalDueAmount
	}

	// 4. 获取订单总减免金额
	totalOrderWaiveAmount, err := s.billModel.GetTotalWaiveAmountByOrderID(bill.OrderID)
	if err != nil {
		return fmt.Errorf("查询订单总减免金额失败: %v", err)
	}

	// 计算订单新的已还金额
	newOrderAmountPaid := order.AmountPaid + model.Decimal(paidAmount)
	if newOrderAmountPaid > order.TotalRepayableAmount {
		newOrderAmountPaid = order.TotalRepayableAmount
	}

	// 计算订单有效还款金额（已还金额 + 本次支付金额 + 总减免金额）
	orderEffectivePayment := newOrderAmountPaid + model.Decimal(totalOrderWaiveAmount.Float64())

	// 5. 判断账单是否逾期
	now := time.Now()
	isOverdue := now.After(bill.DueDate)

	// 6. 根据逾期状态和有效还款金额确定账单新状态
	var newBillStatus int
	if billEffectivePayment >= bill.TotalDueAmount {
		// 完全还清
		if isOverdue {
			newBillStatus = model.RepaymentBillStatusOverduePaid // 逾期已支付
		} else {
			newBillStatus = model.RepaymentBillStatusPaid // 已支付
		}
	} else if billEffectivePayment > 0 {
		// 部分还款
		if isOverdue {
			newBillStatus = model.RepaymentBillStatusOverduePartialPaid // 逾期部分支付
		} else {
			newBillStatus = model.RepaymentBillStatusPartialPaid // 部分还款
		}
	} else {
		// 未还款
		if isOverdue {
			newBillStatus = model.RepaymentBillStatusOverdueUnpaid // 逾期待支付
		} else {
			newBillStatus = model.RepaymentBillStatusUnpaid // 待支付
		}
	}

	// 7. 计算订单新状态
	newOrderStatus := order.Status
	if orderEffectivePayment >= order.TotalRepayableAmount {
		// 订单已完全还清
		newOrderStatus = model.OrderStatusCompleted // 交易完成
	}

	// 8. 开启事务，同时更新账单和订单的已还金额
	err = model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		// 更新还款账单的已还金额和状态
		billUpdateMap := map[string]interface{}{
			"paid_amount": newBillPaidAmount,
			"status":      newBillStatus,
		}
		_, err := tx.Table("business_repayment_bills").Where("id", billID).Update(billUpdateMap)
		if err != nil {
			return fmt.Errorf("更新还款账单已还金额和状态失败: %v", err)
		}

		// 更新订单的已还金额和状态
		orderUpdateMap := map[string]interface{}{
			"amount_paid": newOrderAmountPaid,
			"status":      newOrderStatus,
		}
		_, err = tx.Table("business_loan_orders").Where("id", bill.OrderID).Update(orderUpdateMap)
		if err != nil {
			return fmt.Errorf("更新订单已还金额和状态失败: %v", err)
		}
		return nil
	})
	if err != nil {
		return fmt.Errorf("事务执行失败: %v", err)
	}

	return nil
}

// getStatusString 将状态码转换为状态字符串
func getStatusString(status int) string {
	switch status {
	case model.TransactionStatusPending:
		return "pending"
	case model.TransactionStatusSubmitted:
		return "submitted"
	case model.TransactionStatusSuccess:
		return "success"
	case model.TransactionStatusFailed:
		return "failed"
	default:
		return "unknown"
	}
}

// CheckTimeoutTransactions 定时任务：查询并处理超时的已提交流水
func (s *PaymentService) CheckTimeoutTransactions() error {
	// 1. 查询超时的已提交流水（例如：30分钟前提交但仍未收到回调的交易）
	timeoutThreshold := time.Now().Add(-30 * time.Minute)

	transactions, err := s.transactionModel.GetTimeoutSubmittedTransactions(timeoutThreshold)
	if err != nil {
		return fmt.Errorf("查询超时交易失败: %v", err)
	}

	// 2. 逐个查询并处理
	for _, transaction := range transactions {
		log.Info("处理超时交易 transaction_no=%s", transaction.TransactionNo)

		if _, err := s.QueryPaymentStatus(transaction.TransactionNo); err != nil {
			log.Error("处理超时交易失败 transaction_no=%s error=%v", transaction.TransactionNo, err)
			// 继续处理下一个，不中断整个任务
			continue
		}

		log.Info("超时交易处理完成 transaction_no=%s", transaction.TransactionNo)
	}

	return nil
}

// handlePaymentProcessing 处理支付处理中回调
func (s *PaymentService) handlePaymentProcessing(transaction *model.BusinessPaymentTransactions, req *PaymentCallbackRequest, callbackResult string) error {
	// 处理中状态，更新回调结果但不改变业务状态
	updateMap := map[string]interface{}{
		"callback_result": callbackResult,
	}

	updateTransactionErr := s.transactionModel.UpdateTransactionStatus(nil, model.UpdateTransactionStatusResultWhere{
		ID: transaction.ID,
	}, updateMap)

	if updateTransactionErr != nil {
		return fmt.Errorf("更新流水回调结果失败: %v", updateTransactionErr)
	}

	// 处理中状态不做其他操作，等待补偿机制处理
	return nil
}

// checkAndUpdateOrderStatus 检查并更新订单状态
func (s *PaymentService) checkAndUpdateOrderStatus(tx gform.IOrm, orderID int, amountPaid model.Decimal, totalRepayableAmount model.Decimal, orderUpdateMap map[string]interface{}) error {
	// 订单已经还清
	if amountPaid >= totalRepayableAmount {
		orderUpdateMap["status"] = model.OrderStatusCompleted
		now := time.Now()
		orderUpdateMap["completed_at"] = now

		// 可以在这里添加订单完成后的其他业务逻辑
		// 例如：发送通知、更新用户信用等
	}

	return nil
}

// CreateRepayment 创建还款支付（用户主动还款）
func (s *PaymentService) CreateRepayment(ctx *gin.Context, billID int, bankCardID int) (*CreateRepaymentResponse, error) {
	log.Info("开始处理还款支付 bill_id=%d bank_card_id=%d", billID, bankCardID)

	// 1. 获取当前登录用户ID
	claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
	if claim == nil {
		return nil, errors.New("用户未登录")
	}
	currentUserID := claim.ID

	// 2. 调用内部还款方法
	return s.createRepaymentInternal(int(currentUserID), billID, bankCardID, false)
}

// createRepaymentInternal 内部还款方法（兼容用户主动还款和系统自动代扣）
func (s *PaymentService) createRepaymentInternal(userID int, billID int, bankCardID int, isSystemWithhold bool) (*CreateRepaymentResponse, error) {
	// 1. 查询还款账单信息
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return nil, fmt.Errorf("查询还款账单失败: %v", err)
	}

	// 2. 验证账单所属用户
	if bill.UserID != userID {
		return nil, errors.New("无权限操作此账单")
	}

	// 3. 验证银行卡属于当前用户
	bankCardService := model.NewBusinessBankCardsService(s.ctx)
	bankCard, err := bankCardService.GetBankCardsByCondition(model.QueryBankCardsParams{
		CardID: bankCardID,
	})
	if err != nil {
		return nil, fmt.Errorf("查询银行卡信息失败: %v", err)
	}
	// 检查是否找到记录
	if bankCard == nil {
		return nil, errors.New("银行卡不存在")
	}
	if bankCard.UserID != userID {
		return nil, errors.New("银行卡不属于当前用户")
	}

	// 4. 处理还款逻辑
	response := &CreateRepaymentResponse{}

	// 5. 确定交易类型
	transactionType := model.TransactionTypeRepayment
	if isSystemWithhold {
		transactionType = model.TransactionTypeWithhold
	}

	// 6.1 发起资管费支付
	if bill.AssetManagementEntry > 0 {
		assetResult, err := s.processRepayment(
			userID,
			billID,
			bankCardID,
			float64(bill.AssetManagementEntry),
			WithholdTypeAsset,
			transactionType,
			"",
			0,
		)
		if err != nil {
			log.Error("创建资管费支付失败: %v", err)
		}
		response.AssetPayment = assetResult
	}

	// 6.2 发起担保费支付
	if bill.DueGuaranteeFee > 0 {
		guaranteeResult, err := s.processRepayment(
			userID,
			billID,
			bankCardID,
			float64(bill.DueGuaranteeFee),
			WithholdTypeGuarantee,
			transactionType,
			"",
			0,
		)
		if err != nil {
			log.Error("创建担保费支付失败: %v", err)
		}
		response.GuaranteePayment = guaranteeResult
	}
	if response.GuaranteePayment == nil && response.AssetPayment == nil {
		return nil, errors.New("创建还款支付失败")
	}

	log.Info("还款支付创建完成 bill_id=%d is_system_withhold=%v", billID, isSystemWithhold)
	return response, nil
}

// SystemAutoWithholdBatch 系统自动批量代扣（内部方法，不对外暴露）
func (s *PaymentService) SystemAutoWithholdBatch() (*SystemAutoWithholdBatchResponse, error) {
	log.Info("开始执行系统自动批量代扣")

	// 1. 查询需要代扣的账单
	bills, err := s.getPendingWithholdBills()
	if err != nil {
		return nil, fmt.Errorf("查询待代扣账单失败: %v", err)
	}

	if len(bills) == 0 {
		log.Info("未找到需要代扣的账单")
		return &SystemAutoWithholdBatchResponse{
			TotalCount:   0,
			SuccessCount: 0,
			FailureCount: 0,
			Results:      []SystemAutoWithholdResult{},
		}, nil
	}

	log.Info("找到待代扣账单 count=%d", len(bills))

	// 2. 批量处理代扣
	var results []SystemAutoWithholdResult
	var successCount, failureCount int

	for _, bill := range bills {
		result := s.processSystemAutoWithhold(bill)
		results = append(results, result)

		if result.Success {
			successCount++
		} else {
			failureCount++
		}
	}

	response := &SystemAutoWithholdBatchResponse{
		TotalCount:   len(bills),
		SuccessCount: successCount,
		FailureCount: failureCount,
		Results:      results,
	}

	log.Info("系统自动批量代扣完成 total=%d success=%d failure=%d", len(bills), successCount, failureCount)
	return response, nil
}

// getPendingWithholdBills 获取待代扣的账单
func (s *PaymentService) getPendingWithholdBills() ([]model.BusinessRepaymentBills, error) {
	// 查询状态为：0-待支付、2-逾期已支付、3-逾期待支付、7-部分还款的账单
	targetStatuses := []int{
		model.RepaymentBillStatusUnpaid,        // 0-待支付
		model.RepaymentBillStatusOverdueUnpaid, // 3-逾期待支付
		model.RepaymentBillStatusPartialPaid,   // 7-部分还款
	}

	return s.billModel.GetBillsByStatus(targetStatuses)
}

// processSystemAutoWithhold 处理单个账单的系统自动代扣
func (s *PaymentService) processSystemAutoWithhold(bill model.BusinessRepaymentBills) SystemAutoWithholdResult {
	result := SystemAutoWithholdResult{
		BillID:  bill.ID,
		UserID:  bill.UserID,
		OrderID: bill.OrderID,
		Success: false,
	}

	// 1. 查询用户的银行卡
	bankCards, err := s.getUserBankCards(bill.UserID)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("查询用户银行卡失败: %v", err)
		log.Error("查询用户银行卡失败 user_id=%d error=%v", bill.UserID, err)
		return result
	}

	if len(bankCards) == 0 {
		result.ErrorMessage = "用户未绑定银行卡"
		log.Warn("用户未绑定银行卡 user_id=%d", bill.UserID)
		return result
	}

	// 2. 逐一尝试用户的每张银行卡进行代扣
	var lastErr error
	for i, bankCard := range bankCards {
		// 只尝试已绑定状态的银行卡
		if bankCard.CardStatus != model.CardStatusBound {
			log.Info("跳过未绑定银行卡 bank_card_id=%d status=%d", bankCard.ID, bankCard.CardStatus)
			continue
		}

		log.Info("尝试使用银行卡进行系统自动代扣 bill_id=%d user_id=%d bank_card_id=%d (第%d张)", bill.ID, bill.UserID, bankCard.ID, i+1)

		// 3. 调用内部还款方法进行代扣
		repaymentResponse, err := s.createRepaymentInternal(bill.UserID, bill.ID, bankCard.ID, true)
		if err != nil {
			lastErr = err
			log.Error("银行卡代扣失败 bill_id=%d user_id=%d bank_card_id=%d error=%v", bill.ID, bill.UserID, bankCard.ID, err)
			continue
		}

		// 4. 检查代扣结果状态
		isSuccess := s.checkRepaymentSuccess(repaymentResponse)
		if !isSuccess {
			lastErr = fmt.Errorf("代扣支付失败")
			log.Error("银行卡代扣支付失败 bill_id=%d user_id=%d bank_card_id=%d", bill.ID, bill.UserID, bankCard.ID)
			continue
		}

		// 5. 代扣成功，记录结果并返回
		result.Success = true
		result.BankCardID = bankCard.ID
		result.RepaymentResponse = repaymentResponse

		log.Info("系统自动代扣成功 bill_id=%d user_id=%d bank_card_id=%d", bill.ID, bill.UserID, bankCard.ID)
		return result
	}

	// 6. 所有银行卡都尝试失败
	if lastErr != nil {
		result.ErrorMessage = fmt.Sprintf("所有银行卡代扣均失败，最后错误: %v", lastErr)
	} else {
		result.ErrorMessage = "没有可用的银行卡进行代扣"
	}
	log.Error("系统自动代扣失败 bill_id=%d user_id=%d error=%s", bill.ID, bill.UserID, result.ErrorMessage)
	return result
}

// getUserBankCards 获取用户的银行卡列表
func (s *PaymentService) getUserBankCards(userID int) ([]model.BusinessBankCards, error) {
	bankCardService := model.NewBusinessBankCardsService(s.ctx)
	return bankCardService.GetBankCardsByUserID(userID)
}

// checkRepaymentSuccess 检查还款响应是否成功
func (s *PaymentService) checkRepaymentSuccess(repaymentResponse *CreateRepaymentResponse) bool {
	if repaymentResponse == nil {
		return false
	}

	// 检查担保费支付结果
	if repaymentResponse.GuaranteePayment != nil {
		if repaymentResponse.GuaranteePayment.Status == "failed" {
			log.Error("担保费支付失败: %s", repaymentResponse.GuaranteePayment.Message)
			return false
		}
	}

	// 检查资管费支付结果
	if repaymentResponse.AssetPayment != nil {
		if repaymentResponse.AssetPayment.Status == "failed" {
			log.Error("资管费支付失败: %s", repaymentResponse.AssetPayment.Message)
			return false
		}
	}

	// 如果有支付项目且都不是失败状态，则认为成功
	return true
}

// generatePaymentTransactionNo 生成支付唯一流水号
// 格式: RP + 时间戳(毫秒) + 随机数，总长度不超过32字符
func generatePaymentTransactionNo() string {
	// 使用毫秒时间戳(13位) + 6位随机数，总长度: RP(2) + 13 + 6 = 21字符
	timestamp := time.Now().UnixNano() / 1000000 // 毫秒时间戳
	randomNum := rand.Intn(1000000)              // 6位随机数
	return fmt.Sprintf("RP%d%06d", timestamp, randomNum)
}

// getSystemIP 获取系统IP地址
func getSystemIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

// updateTransactionStatus 更新交易状态
func (s *PaymentService) updateTransactionStatus(transactionNo string, status int, message string) error {
	updateMap := map[string]interface{}{
		"status":        status,
		"error_message": message,
	}
	where := model.UpdateTransactionStatusResultWhere{
		TransactionNo: transactionNo,
	}
	return s.transactionModel.UpdateTransactionStatus(nil, where, updateMap)
}

// updateTransactionStatusWithDetails 更新交易状态（包含详细信息）
func (s *PaymentService) updateTransactionStatusWithDetails(transactionNo string, status int, message string, channelTransactionNo string, thirdPartyOrderNo string) error {
	log.Info("开始更新交易状态 transaction_no=%s status=%d channel_transaction_no=%s third_party_order_no=%s", transactionNo, status, channelTransactionNo, thirdPartyOrderNo)
	updateMap := map[string]interface{}{
		"status":                 status,
		"error_message":          message,
		"channel_transaction_no": channelTransactionNo,
		"third_party_order_no":   thirdPartyOrderNo,
	}
	where := model.UpdateTransactionStatusResultWhere{
		TransactionNo: transactionNo,
	}
	err := s.transactionModel.UpdateTransactionStatus(nil, where, updateMap)
	if err != nil {
		log.Error("更新交易状态失败 transaction_no=%s error=%v", transactionNo, err)
		return err
	}
	log.Info("更新交易状态成功 transaction_no=%s status=%d", transactionNo, status)
	return nil
}

// getPaymentService 获取支付服务实例
func getPaymentService() (sumpay.SumpayServiceInterface, error) {
	return sumpay.NewSumpayService()
}

// GetMerNoByWithholdType 根据业务类型获取对应的商户号
func GetMerNoByWithholdType(withholdType string) string {
	switch withholdType {
	case WithholdTypeAsset:
		return global.App.Config.SumPay.AssetMerNo
	case WithholdTypeGuarantee:
		return global.App.Config.SumPay.GuaranteeMerNo
	default:
		// 默认使用原有的商户号（保持兼容性）
		return global.App.Config.SumPay.MerNo
	}
}

// ManualWithhold 管理员手动代扣
func (s *PaymentService) ManualWithhold(ctx *gin.Context, billID int, bankCardID int, amount float64, remark string, withholdType string, adminID int) (*PaymentResult, error) {
	log.Info("开始处理手动代扣 bill_id=%d bank_card_id=%d amount=%.2f admin_id=%d", billID, bankCardID, amount, adminID)

	// 1. 通过bill_id查询账单信息获取user_id
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return nil, fmt.Errorf("查询账单信息失败: %v", err)
	}
	if bill == nil {
		return nil, fmt.Errorf("账单不存在")
	}

	// 2. 验证指定的银行卡是否存在且属于该用户
	bankCardService := model.NewBusinessBankCardsService(s.ctx)
	bankCard, err := bankCardService.GetBankCardsByCondition(model.QueryBankCardsParams{CardID: bankCardID})
	if err != nil {
		return nil, fmt.Errorf("查询银行卡信息失败: %v", err)
	}
	if bankCard == nil {
		return nil, fmt.Errorf("银行卡不存在")
	}
	if bankCard.UserID != bill.UserID {
		return nil, fmt.Errorf("银行卡不属于该用户")
	}
	if bankCard.CardStatus != model.CardStatusBound {
		return nil, fmt.Errorf("银行卡未绑定，无法进行代扣")
	}

	log.Info("使用指定银行卡进行手动代扣 bank_card_id=%d bank_name=%s", bankCard.ID, bankCard.BankName)

	// 3. 使用指定银行卡进行扣款
	result, err := s.processRepayment(
		0, // 手动代扣不需要用户ID验证
		billID,
		bankCard.ID,
		amount,
		withholdType,
		model.TransactionTypeManualWithhold,
		remark,
		adminID,
	)

	if err != nil {
		log.Error("指定银行卡扣款失败 bank_card_id=%d error=%v", bankCard.ID, err)
		return nil, fmt.Errorf("银行卡扣款失败: %v", err)
	}

	// 扣款成功，返回结果
	log.Info("手动代扣成功 transaction_no=%s bank_card_id=%d amount=%.2f", result.TransactionNo, bankCard.ID, amount)
	return result, nil
}

// processRepayment 通用还款处理方法
func (s *PaymentService) processRepayment(
	userID int,
	billID int,
	bankCardID int,
	amount float64,
	withholdType string,
	transactionType string,
	remark string,
	adminID int,
) (*PaymentResult, error) {
	// 🔒 添加还款维度锁，防止并发操作
	lockKey := fmt.Sprintf("repayment:bill:%d", billID)
	repaymentLock := lock.GetLock(lockKey, 30*time.Second)
	log.Info("还款加锁成功 lock_key=%s", lockKey)

	repaymentLock.Lock()
	defer repaymentLock.Unlock()

	// 1. 验证并获取支付相关数据
	bill, bankCard, order, err := s.validateAndGetPaymentData(billID, bankCardID, amount, withholdType)
	if err != nil {
		return nil, err
	}

	// 2. 获取系统信息
	systemIP := getSystemIP()
	orderTime := time.Now().Format("**************")

	// 3. 生成交易流水号和支付订单号
	transactionNo := generatePaymentTransactionNo()
	payOrderNo := GeneratePayOrderNo(order.OrderNo, transactionNo, withholdType)

	// 4. 创建支付
	result, err := s.createSinglePayment(
		bill.UserID,
		bill.OrderID,
		billID,
		amount,
		withholdType,
		bankCard,
		order,
		systemIP,
		orderTime,
		payOrderNo,
		transactionNo,
		transactionType,
	)

	if err != nil {
		return nil, err
	}

	// 5. 对于手动代扣，记录管理员操作日志
	if adminID > 0 {
		operationDetail := map[string]interface{}{
			"transaction_no": result.TransactionNo,
			"amount":         amount,
			"withhold_type":  "MANUAL_WITHHOLD",
			"remark":         remark,
			"bill_id":        billID,
			"bank_card_id":   bankCard.ID,
			"bank_name":      bankCard.BankName,
		}

		detailJSON, _ := json.Marshal(operationDetail)
		operationLog := &model.BusinessOrderOperationLogs{
			OrderID:      bill.OrderID,
			OperatorID:   adminID,
			OperatorName: fmt.Sprintf("管理员%d", adminID),
			Action:       "手动代扣",
			Details:      string(detailJSON),
			CreatedAt:    time.Now().Unix(),
		}

		// 记录操作日志
		if logErr := s.operationModel.CreateLog(operationLog); logErr != nil {
			log.Error("记录操作日志失败 error=%v", logErr)
		}
	}

	return result, nil
}

// createSinglePayment 创建单笔支付
func (s *PaymentService) createSinglePayment(
	userID int,
	orderID int,
	billID int,
	amount float64,
	withholdType string,
	bankCard *model.BusinessBankCards,
	order *model.BusinessLoanOrders,
	systemIP string,
	orderTime string,
	payOrderNo string,
	transactionNo string,
	transactionType string,
) (*PaymentResult, error) {
	// 1. 使用传入的交易流水号

	// 2. 创建支付流水记录
	transaction := &model.BusinessPaymentTransactions{
		TransactionNo:     transactionNo,
		ThirdPartyOrderNo: payOrderNo,
		OrderID:           orderID,
		OrderNo:           order.OrderNo,
		BillID:            &billID,
		UserID:            userID,
		PaymentChannelID:  getPaymentChannelID(order),
		Type:              transactionType,
		WithholdType:      getWithholdTypePtr(withholdType),
		Amount:            model.Decimal(amount),
		Status:            model.TransactionStatusPending,
		CreatedAt:         time.Now(),
	}

	err := s.transactionModel.CreateTransaction(transaction)
	if err != nil {
		return nil, fmt.Errorf("创建支付流水失败: %v", err)
	}

	// 3. 构建支付请求
	orderNoForPay := payOrderNo
	if orderNoForPay == "" {
		orderNoForPay = transactionNo
	}

	paymentRequest := sumpay.NewPayRequest(
		fmt.Sprintf("%d", order.UserID),
		systemIP,
		orderNoForPay,
		orderTime,
		fmt.Sprintf("订单%d-%s还款", orderID, withholdType),
		bankCard.BindCardId,
		amount,
		PaymentGoodsType,
		PaymentGoodsNum,
		GetMerNoByWithholdType(withholdType),
	)

	// 4. 调用支付服务并处理结果
	return s.executePaymentAndHandleResult(transactionNo, billID, paymentRequest, amount)
}

// executePaymentAndHandleResult 执行支付并处理结果
func (s *PaymentService) executePaymentAndHandleResult(transactionNo string, billID int, paymentRequest *sumpay.PayRequest, amount float64) (*PaymentResult, error) {
	paymentService, err := sumpay.NewSumpayService(sumpay.WithContext(s.ctx))
	if err != nil {
		return nil, fmt.Errorf("获取支付服务失败: %v", err)
	}

	result, err := paymentService.Pay(paymentRequest)
	if err != nil {
		s.updateTransactionStatus(transactionNo, model.TransactionStatusFailed, "支付调用失败")
		return nil, fmt.Errorf("支付调用失败: %v", err)
	}

	if result != nil {
		if result.RespCode == "000000" {
			// 解析支付响应数据
			payResponseData, parseErr := result.GetPayResponseData()
			if parseErr != nil {
				s.updateTransactionStatus(transactionNo, model.TransactionStatusFailed, "处理响应数据失败")
				return nil, fmt.Errorf("处理响应数据失败: %v", parseErr)
			}
			// 使用新的方法更新交易状态，包含渠道交易号和第三方订单号
			if updateErr := s.updateTransactionStatusWithDetails(transactionNo, model.TransactionStatusSubmitted, "", payResponseData.SerialNo, payResponseData.OrderNo); updateErr != nil {
				return nil, fmt.Errorf("更新交易状态失败: %v", updateErr)
			}
			log.Info("支付请求提交成功 transaction_no=%s third_party_order_no=%s channel_transaction_no=%s status=%s", transactionNo, payResponseData.OrderNo, payResponseData.SerialNo, payResponseData.Status)
			// 查询支付状态
			queryResult, err := s.QueryPaymentStatus(transactionNo)
			if err != nil {
				return nil, fmt.Errorf("查询支付状态失败: %v", err)
			}
			log.Info("查询支付状态 transaction_no=%s status=%s", transactionNo, queryResult.Status)

			// 根据查询结果构造返回信息
			var status, message string
			switch queryResult.Status {
			case "success":
				status = "success"
				message = "支付成功"
				log.Info("支付成功 transaction_no=%s status=%s", transactionNo, queryResult.Status)
			case "failed":
				status = "failed"
				message = "支付失败"
				if queryResult.ErrorMessage != "" {
					message = fmt.Sprintf("支付失败: %s", queryResult.ErrorMessage)
				}
			case "submitted":
				status = "submitted"
				message = "支付提交成功"
			default:
				status = "submitted"
				message = "支付提交成功"
			}

			return &PaymentResult{
				TransactionNo: transactionNo,
				Amount:        amount,
				Status:        status,
				Message:       message,
			}, nil
		} else {
			errorMsg := fmt.Sprintf("支付失败: %s", result.RespMsg)
			s.updateTransactionStatus(transactionNo, model.TransactionStatusFailed, errorMsg)
			return nil, fmt.Errorf("%s", errorMsg)
		}
	} else {
		s.updateTransactionStatus(transactionNo, model.TransactionStatusFailed, "支付平台返回空结果")
		return nil, fmt.Errorf("支付平台返回空结果")
	}
}

// getPaymentChannelID 获取支付渠道ID
func getPaymentChannelID(order *model.BusinessLoanOrders) int {
	if order.PaymentChannelID != nil {
		return int(*order.PaymentChannelID)
	}
	return 0
}

// getWithholdTypePtr 获取代扣类型指针
func getWithholdTypePtr(withholdType string) *string {
	if withholdType == "" {
		return nil
	}
	return &withholdType
}

// validateAndGetPaymentData 验证并获取支付相关数据
func (s *PaymentService) validateAndGetPaymentData(billID, bankCardID int, amount float64, withholdType string) (*model.BusinessRepaymentBills, *model.BusinessBankCards, *model.BusinessLoanOrders, error) {
	// 1. 验证还款金额
	if err := s.ValidateRepaymentAmount(billID, amount, withholdType); err != nil {
		return nil, nil, nil, err
	}

	// 2. 查询账单信息
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("查询账单失败: %v", err)
	}

	// 3. 查询银行卡信息
	bankCard, err := s.bankCardModel.GetBankCardsByCondition(model.QueryBankCardsParams{
		CardID: bankCardID,
	})
	if err != nil {
		return nil, nil, nil, fmt.Errorf("查询银行卡失败: %v", err)
	}
	if bankCard == nil {
		return nil, nil, nil, fmt.Errorf("银行卡不存在")
	}

	// 4. 查询订单信息
	order, err := s.orderModel.GetOrderByID(bill.OrderID)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("查询订单失败: %v", err)
	}

	return bill, bankCard, order, nil
}

// calculateSubmittedAmounts 计算已提交的交易金额（公共方法）
func (s *PaymentService) calculateSubmittedAmounts(transactions []model.BusinessPaymentTransactions) (float64, float64) {
	var guaranteeAmount, assetAmount float64

	// 允许的交易类型
	allowedTypes := map[string]bool{
		model.TransactionTypeRepayment:               true, // REPAYMENT-用户主动还款
		model.TransactionTypeWithhold:                true, // WITHHOLD-系统代扣
		model.TransactionTypePartialOfflineRepayment: true, // PARTIAL_OFFLINE_REPAYMENT-线下部分还款
		model.TransactionTypeManualWithhold:          true, // MANUAL_WITHHOLD-管理员手动代扣
	}

	// 允许的状态
	allowedStatuses := map[int]bool{
		model.TransactionStatusSubmitted: true, // 1-已提交
		model.TransactionStatusSuccess:   true, // 2-处理成功
	}

	for _, transaction := range transactions {
		// 检查交易类型和状态
		if allowedTypes[transaction.Type] && allowedStatuses[transaction.Status] {
			// 根据withhold_type区分担保费和资管费
			if transaction.WithholdType != nil {
				switch *transaction.WithholdType {
				case WithholdTypeGuarantee: // GUARANTEE-担保
					guaranteeAmount += float64(transaction.Amount)
				case WithholdTypeAsset: // ASSET-资管
					assetAmount += float64(transaction.Amount)
				}
			}
		} else if transaction.Type == model.TransactionTypeRefund && allowedStatuses[transaction.Status] {
			// 减去退款金额
			if transaction.WithholdType != nil {
				switch *transaction.WithholdType {
				case WithholdTypeGuarantee:
					guaranteeAmount -= float64(transaction.Amount)
				case WithholdTypeAsset:
					assetAmount -= float64(transaction.Amount)
				}
			}
		}
	}

	return guaranteeAmount, assetAmount
}

// ValidateRepaymentAmount 智能还款金额校验
func (s *PaymentService) ValidateRepaymentAmount(billID int, requestAmount float64, withholdType string) error {
	// 1. 查询账单信息
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return fmt.Errorf("查询账单失败: %v", err)
	}

	// 2. 检查账单状态（仅允许待支付和部分还款）
	allowedStatuses := []int{
		model.RepaymentBillStatusUnpaid,        // 0-待支付
		model.RepaymentBillStatusOverdueUnpaid, // 3-逾期待支付
		model.RepaymentBillStatusPartialPaid,   // 7-部分还款
	}
	statusAllowed := false
	for _, status := range allowedStatuses {
		if bill.Status == status {
			statusAllowed = true
			break
		}
	}
	if !statusAllowed {
		statusText := model.RepaymentBillStatusDescriptions[bill.Status]
		return fmt.Errorf("账单状态不允许还款，当前状态: %s", statusText)
	}

	// 3. 查询已提交流水总额（使用统一的计算逻辑）
	transactions, err := s.transactionModel.GetTransactionsByBillID(billID)
	if err != nil {
		return fmt.Errorf("查询已提交流水失败: %v", err)
	}
	guaranteeAmount, assetAmount := s.calculateSubmittedAmounts(transactions)

	// 4. 根据withholdType类型决定比较逻辑
	var dueAmount, submittedAmount float64
	switch withholdType {
	case WithholdTypeGuarantee:
		// 担保费比较
		dueAmount = float64(bill.DueGuaranteeFee) // 应还担保费
		submittedAmount = guaranteeAmount         // 已提交担保费
		log.Info("担保费校验 due_guarantee_fee=%.2f submitted_guarantee_amount=%.2f", dueAmount, submittedAmount)
	case WithholdTypeAsset:
		// 资管费比较
		dueAmount = float64(bill.AssetManagementEntry) // 应还资管费
		submittedAmount = assetAmount                  // 已提交资管费
		log.Info("资管费校验 due_asset_fee=%.2f submitted_asset_amount=%.2f", dueAmount, submittedAmount)
	default:
		// 其他类型使用总金额比较
		dueAmount = float64(bill.TotalDueAmount)        // 总应还金额
		submittedAmount = guaranteeAmount + assetAmount // 总已提交金额
		log.Info("总金额校验 total_due_amount=%.2f total_submitted_amount=%.2f", dueAmount, submittedAmount)
	}

	remainingAmount := dueAmount - submittedAmount

	// 6. 如果指定了请求金额，验证是否超额
	if requestAmount > 0 && requestAmount > remainingAmount {
		return fmt.Errorf("还款金额%.2f元超过剩余应还金额%.2f元", requestAmount, remainingAmount)
	}

	// 7. 如果剩余金额小于等于0，不允许继续还款
	if remainingAmount <= 0 {
		return fmt.Errorf("该账单已无剩余应还金额")
	}

	return nil
}

// GetSubmittedAmountsByBillID 根据账单ID获取已提交金额
func (s *PaymentService) GetSubmittedAmountsByBillID(billID int) (*SubmittedAmountsResponse, error) {
	// 查询账单信息
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return nil, fmt.Errorf("查询账单失败: %v", err)
	}

	// 查询交易流水
	transactions, err := s.transactionModel.GetTransactionsByBillID(billID)
	if err != nil {
		return nil, fmt.Errorf("查询交易流水失败: %v", err)
	}

	// 使用统一的计算逻辑
	submittedGuaranteeAmount, submittedAssetAmount := s.calculateSubmittedAmounts(transactions)

	// 计算剩余担保费和剩余资管费
	dueGuaranteeFee := float64(bill.DueGuaranteeFee)
	dueAssetFee := float64(bill.AssetManagementEntry)
	remainingGuaranteeFee := dueGuaranteeFee - submittedGuaranteeAmount
	remainingAssetFee := dueAssetFee - submittedAssetAmount

	// 确保剩余金额不为负数
	if remainingGuaranteeFee < 0 {
		remainingGuaranteeFee = 0
	}
	if remainingAssetFee < 0 {
		remainingAssetFee = 0
	}

	log.Info("账单ID=%d 已提交担保费=%.2f 已提交资管费=%.2f 剩余担保费=%.2f 剩余资管费=%.2f",
		billID, submittedGuaranteeAmount, submittedAssetAmount, remainingGuaranteeFee, remainingAssetFee)

	// 向上取整到两位小数的函数
	ceilToTwoDecimal := func(value float64) float64 {
		return math.Ceil(value*100) / 100
	}

	// 构建响应
	response := &SubmittedAmountsResponse{
		BillID:                billID,
		GuaranteeAmount:       ceilToTwoDecimal(submittedGuaranteeAmount), // 已提交担保总和
		AssetAmount:           ceilToTwoDecimal(submittedAssetAmount),     // 已提交资管总和
		TotalAmount:           ceilToTwoDecimal(submittedGuaranteeAmount + submittedAssetAmount),
		RemainingGuaranteeFee: ceilToTwoDecimal(remainingGuaranteeFee), // 剩余担保费
		RemainingAssetFee:     ceilToTwoDecimal(remainingAssetFee),     // 剩余资管费
	}

	return response, nil
}
