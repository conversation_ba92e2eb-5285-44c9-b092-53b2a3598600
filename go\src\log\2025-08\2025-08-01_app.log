{"level":"dev.info","ts":"[2025-08-01 09:50:21.321]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.325]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 09:50:21.490]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.589]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.594]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 10:05:53.626]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.083]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.095]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 10:29:09.141]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.457]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.462]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 10:54:31.519]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.717]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.721]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 11:00:29.807]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.494]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.497]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 11:09:01.531]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.574]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.579]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 11:10:49.624]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.error","ts":"[2025-08-01 11:11:03.036]","caller":"user/index.go:506","msg":"注销账户失败: 用户存在未完结订单 userId=110, inProgressCount=1","stacktrace":"fincore/app/uniapp/user.(*Index).CancelAccount\n\tD:/work/code/fincore/go/src/app/uniapp/user/index.go:506\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.ErrorLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-01 11:11:03.036]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 注销失败，您存在未完结的订单","data":"null"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.369]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.380]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 11:14:43.431]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.064]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.067]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 11:19:52.187]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.371]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.374]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 11:22:51.408]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.627]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.634]","caller":"bootstrap/router.go:31","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 11:38:50.677]","caller":"bootstrap/router.go:31","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.921]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.939]","caller":"bootstrap/router.go:32","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 12:09:37.948]","caller":"bootstrap/router.go:32","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.108]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.111]","caller":"bootstrap/router.go:32","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 14:10:10.277]","caller":"bootstrap/router.go:32","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.977]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 14:10:32.981]","caller":"bootstrap/router.go:32","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 14:10:33.047]","caller":"bootstrap/router.go:32","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.705]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 14:11:47.721]","caller":"bootstrap/router.go:32","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 14:11:48.195]","caller":"bootstrap/router.go:32","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.789]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.795]","caller":"bootstrap/router.go:32","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-01 14:12:31.842]","caller":"bootstrap/router.go:32","msg":"连接数据库成功:1"}
