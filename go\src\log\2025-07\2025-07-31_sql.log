{"level":"dev.info","ts":"[2025-07-31 23:02:45.927]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `contracts` WHERE `user_id` = ? and `product_id` = ? ORDER BY create_time DESC LIMIT 1, [108 15]","duration":"11.6983ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-07-31 23:02:45.943]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT channelId FROM `business_app_account` WHERE `id` = ? LIMIT 1, [108]","duration":"8.9485ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-07-31 23:02:45.950]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [15]","duration":"7.2115ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.037]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"35.2507ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.053]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [LO20241224TEST001]","duration":"15.5458ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.083]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? ORDER BY id DESC LIMIT 1, [14]","duration":"28.4273ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.083]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? LIMIT 1, [14]","duration":"28.9624ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.121]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [14]","duration":"37.7005ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.216]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_id` = ? and `type` = ? ORDER BY created_at DESC, [1 DISBURSEMENT]","duration":"94.0469ms","duration_ms":94}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.260]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_payment_transactions` (`offline_payment_channel_detail`,`user_id`,`callback_result`,`third_party_order_no`,`withhold_type`,`error_code`,`completed_at`,`bill_id`,`status`,`type`,`error_message`,`channel_transaction_no`,`order_no`,`order_id`,`payment_channel_id`,`created_at`,`related_transaction_no`,`transaction_no`,`offline_payment_voucher`,`amount`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [<nil> 14 <nil>  <nil>  <nil> <nil> 0 DISBURSEMENT   LO20241224TEST001 1 1 2025-07-31 23:02:50.2419222 +0800 CST m=+10.068202101 <nil> D1753974170241922200598007 <nil> 5000 <nil>]","duration":"18.4715ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.485]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `callback_result` = ?,`status` = ?,`error_message` = ?,`error_code` = ? WHERE `transaction_no` = ?, [{\"resp_code\":\"400002\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"sign_type\":\"CERT\"} 3 参数非法，银行卡号不能为空[AM999998] 400002 D1753974170241922200598007]","duration":"26.4174ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.494]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_order_operation_logs` (`action`,`details`,`created_at`,`order_id`,`operator_id`,`operator_name`) VALUES (?,?,?,?,?,?), [已提交系统自动 订单编号: LO20241224TEST001, 放款金额: 5000.00, 失败原因: 参数非法，银行卡号不能为空[AM999998] 2025-07-31 23:02:50.4864806 +0800 CST m=+10.********* 1 0 系统自动放款补偿]","duration":"7.9028ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.507]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [LO20241224DEMO002]","duration":"12.3071ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.521]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? ORDER BY id DESC LIMIT 1, [16]","duration":"13.5574ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.521]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? LIMIT 1, [16]","duration":"13.5574ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.550]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [16]","duration":"27.8138ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.596]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_id` = ? and `type` = ? ORDER BY created_at DESC, [3 DISBURSEMENT]","duration":"44.7478ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.649]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_payment_transactions` (`channel_transaction_no`,`offline_payment_voucher`,`callback_result`,`error_message`,`completed_at`,`user_id`,`related_transaction_no`,`order_no`,`created_at`,`transaction_no`,`status`,`withhold_type`,`order_id`,`third_party_order_no`,`remark`,`error_code`,`offline_payment_channel_detail`,`bill_id`,`type`,`amount`,`payment_channel_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [ <nil> <nil>  <nil> 16 <nil> LO20241224DEMO002 2025-07-31 23:02:50.627894 +0800 CST m=+10.454173901 D1753974170627894000001901 0 <nil> 3  <nil>  <nil> <nil> DISBURSEMENT 5000 1]","duration":"21.9432ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.848]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`error_message` = ?,`error_code` = ?,`callback_result` = ? WHERE `transaction_no` = ?, [3 参数非法，银行卡号不能为空[AM999998] 400002 {\"resp_code\":\"400002\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"sign_type\":\"CERT\"} D1753974170627894000001901]","duration":"46.5543ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.856]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_order_operation_logs` (`operator_id`,`operator_name`,`action`,`details`,`created_at`,`order_id`) VALUES (?,?,?,?,?,?), [0 系统自动放款补偿 已提交系统自动 订单编号: LO20241224DEMO002, 放款金额: 5000.00, 失败原因: 参数非法，银行卡号不能为空[AM999998] 2025-07-31 23:02:50.8488226 +0800 CST m=+10.********* 3]","duration":"8.1252ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.866]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [TEST_AUTO_001]","duration":"7.3643ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.891]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? ORDER BY id DESC LIMIT 1, [17]","duration":"24.5279ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.891]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? LIMIT 1, [17]","duration":"24.5279ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.906]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [17]","duration":"15.3421ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.968]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_id` = ? and `type` = ? ORDER BY created_at DESC, [5 DISBURSEMENT]","duration":"61.2801ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.011]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_payment_transactions` (`transaction_no`,`user_id`,`offline_payment_channel_detail`,`error_message`,`created_at`,`offline_payment_voucher`,`bill_id`,`withhold_type`,`channel_transaction_no`,`type`,`completed_at`,`related_transaction_no`,`amount`,`status`,`order_id`,`callback_result`,`error_code`,`remark`,`order_no`,`third_party_order_no`,`payment_channel_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [D1753974171003138500564855 17 <nil>  2025-07-31 23:02:51.0031385 +0800 CST m=+10.829418401 <nil> <nil> <nil>  DISBURSEMENT <nil> <nil> 5000 0 5 <nil>  <nil> TEST_AUTO_001  1]","duration":"8.3566ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.199]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `error_message` = ?,`error_code` = ?,`callback_result` = ?,`status` = ? WHERE `transaction_no` = ?, [参数非法，银行卡号不能为空[AM999998] 400002 {\"resp_code\":\"400002\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"sign_type\":\"CERT\"} 3 D1753974171003138500564855]","duration":"24.6485ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.210]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_order_operation_logs` (`order_id`,`operator_id`,`operator_name`,`action`,`details`,`created_at`) VALUES (?,?,?,?,?,?), [5 0 系统自动放款补偿 已提交系统自动 订单编号: TEST_AUTO_001, 放款金额: 5000.00, 失败原因: 参数非法，银行卡号不能为空[AM999998] 2025-07-31 23:02:51.2006089 +0800 CST m=+11.*********]","duration":"9.376ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.221]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [LO20250730DHEAQTP5]","duration":"10.3666ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.243]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? ORDER BY id DESC LIMIT 1, [92]","duration":"20.3139ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-07-31 23:02:51.245]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? LIMIT 1, [92]","duration":"22.5118ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.316]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `risk_raw_data` (`data_source`,`evaluation_id`,`leida_v4_response`,`tan_zhen_c_response`,`zwsc_response`) VALUES (?,?,?,?,?), [third_party EVAL_108_1753974196293103900   ]","duration":"23.4133ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.334]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `risk_evaluations` (`risk_result`,`evaluation_time`,`evaluation_id`,`customer_id`,`risk_score`) VALUES (?,?,?,?,?), [1 2025-07-31 23:03:16.3170707 +0800 CST m=+36.143350601 EVAL_108_1753974196293103900 108 500]","duration":"17.123ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.350]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [15]","duration":"16.1858ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.361]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"UPDATE `business_repayment_bills` SET `status` = ?,`updated_at` = ? WHERE `order_id` = ?, [4 2025-07-31 23:02:46.2737437 +0800 CST m=+6.100023601 114]","duration":"10.409ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.027]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"26.3444ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.038]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [LO20241224TEST001]","duration":"11.7682ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.052]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? LIMIT 1, [14]","duration":"12.7937ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.053]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? ORDER BY id DESC LIMIT 1, [14]","duration":"13.8685ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.060]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [14]","duration":"8.1362ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.143]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_id` = ? and `type` = ? ORDER BY created_at DESC, [1 DISBURSEMENT]","duration":"81.9115ms","duration_ms":81}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.218]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_payment_transactions` (`offline_payment_channel_detail`,`bill_id`,`completed_at`,`order_id`,`amount`,`transaction_no`,`channel_transaction_no`,`offline_payment_voucher`,`callback_result`,`created_at`,`error_message`,`remark`,`order_no`,`third_party_order_no`,`payment_channel_id`,`withhold_type`,`related_transaction_no`,`status`,`error_code`,`type`,`user_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [<nil> <nil> <nil> 1 5000 D1753974200181672800236198  <nil> <nil> 2025-07-31 23:03:20.1816728 +0800 CST m=+40.007952701  <nil> LO20241224TEST001  1 <nil> <nil> 0  DISBURSEMENT 14]","duration":"35.527ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.419]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`error_message` = ?,`error_code` = ?,`callback_result` = ? WHERE `transaction_no` = ?, [3 参数非法，银行卡号不能为空[AM999998] 400002 {\"resp_code\":\"400002\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"sign_type\":\"CERT\"} D1753974200181672800236198]","duration":"9.4883ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.433]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_order_operation_logs` (`details`,`created_at`,`order_id`,`operator_id`,`operator_name`,`action`) VALUES (?,?,?,?,?,?), [订单编号: LO20241224TEST001, 放款金额: 5000.00, 失败原因: 参数非法，银行卡号不能为空[AM999998] 2025-07-31 23:03:20.419819 +0800 CST m=+40.********* 1 0 系统自动放款补偿 已提交系统自动]","duration":"13.4916ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.442]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [LO20241224DEMO002]","duration":"8.0581ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.453]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? ORDER BY id DESC LIMIT 1, [16]","duration":"10.9113ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.456]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? LIMIT 1, [16]","duration":"13.1405ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.464]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [16]","duration":"8.2969ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.528]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_id` = ? and `type` = ? ORDER BY created_at DESC, [3 DISBURSEMENT]","duration":"63.7749ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.577]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_payment_transactions` (`error_code`,`callback_result`,`type`,`error_message`,`offline_payment_voucher`,`remark`,`channel_transaction_no`,`payment_channel_id`,`amount`,`order_id`,`status`,`withhold_type`,`bill_id`,`related_transaction_no`,`order_no`,`transaction_no`,`user_id`,`offline_payment_channel_detail`,`third_party_order_no`,`completed_at`,`created_at`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [ <nil> DISBURSEMENT  <nil> <nil>  1 5000 3 0 <nil> <nil> <nil> LO20241224DEMO002 D1753974200567744200417683 16 <nil>  <nil> 2025-07-31 23:03:20.5677442 +0800 CST m=+40.394024101]","duration":"9.2898ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.738]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`error_message` = ?,`error_code` = ?,`callback_result` = ? WHERE `transaction_no` = ?, [3 参数非法，银行卡号不能为空[AM999998] 400002 {\"resp_code\":\"400002\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"sign_type\":\"CERT\"} D1753974200567744200417683]","duration":"10.4298ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.746]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_order_operation_logs` (`order_id`,`operator_id`,`operator_name`,`action`,`details`,`created_at`) VALUES (?,?,?,?,?,?), [3 0 系统自动放款补偿 已提交系统自动 订单编号: LO20241224DEMO002, 放款金额: 5000.00, 失败原因: 参数非法，银行卡号不能为空[AM999998] 2025-07-31 23:03:20.7382379 +0800 CST m=+40.*********]","duration":"7.9873ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.758]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [TEST_AUTO_001]","duration":"10.9623ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.780]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? ORDER BY id DESC LIMIT 1, [17]","duration":"22.2398ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.780]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? LIMIT 1, [17]","duration":"22.2398ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.793]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [17]","duration":"12.7944ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.835]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE (deleted_at IS NULL) and `order_id` = ? and `type` = ? ORDER BY created_at DESC, [5 DISBURSEMENT]","duration":"41.8534ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.885]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_payment_transactions` (`created_at`,`callback_result`,`error_code`,`offline_payment_channel_detail`,`third_party_order_no`,`user_id`,`transaction_no`,`offline_payment_voucher`,`channel_transaction_no`,`order_id`,`type`,`payment_channel_id`,`remark`,`status`,`amount`,`error_message`,`withhold_type`,`order_no`,`related_transaction_no`,`completed_at`,`bill_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [2025-07-31 23:03:20.8593025 +0800 CST m=+40.685582401 <nil>  <nil>  17 D1753974200859302500260361 <nil>  5 DISBURSEMENT 1 <nil> 0 5000  <nil> TEST_AUTO_001 <nil> <nil> <nil>]","duration":"25.7885ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.043]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"UPDATE `business_payment_transactions` SET `status` = ?,`error_message` = ?,`error_code` = ?,`callback_result` = ? WHERE `transaction_no` = ?, [3 参数非法，银行卡号不能为空[AM999998] 400002 {\"resp_code\":\"400002\",\"resp_msg\":\"参数非法，银行卡号不能为空[AM999998]\",\"sign\":\"Lxia+p4zMrTCp5F32yLzii0oQuyrzBxMZkpZWssmlkwcq2Zf1ptALhkEMrWCkXDixfT5CT7cQ3+hESviy1QuOS+a9U4KZES5V2iM8w7SGUdZkEhe1PEUWN88KSn/6pMphjR/MTXRiENp8BrIntgpGqGjiug6XSzTHXvORn9I2EHDQ5In+Zu2H+gjXcSxJZK6hWyi6lawUCYjmvqfom0gwq3qJG5VDdmhhaK2UGkGPaRFr8SVKysvFVEvww7Ik6AR8tujbNhzlOf5sPzmwMeFFaIIEbstbMXPphzaIhUjA6bJw4gb+7xGSIKBcQHe8bFhQg3DGDlJ0xjI4x2LAm03Sw==\",\"sign_type\":\"CERT\"} D1753974200859302500260361]","duration":"10.2096ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.053]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"INSERT INTO `business_order_operation_logs` (`order_id`,`operator_id`,`operator_name`,`action`,`details`,`created_at`) VALUES (?,?,?,?,?,?), [5 0 系统自动放款补偿 已提交系统自动 订单编号: TEST_AUTO_001, 放款金额: 5000.00, 失败原因: 参数非法，银行卡号不能为空[AM999998] 2025-07-31 23:03:21.0445636 +0800 CST m=+40.*********]","duration":"8.4972ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.067]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [LO20250730DHEAQTP5]","duration":"14.2128ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.087]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? ORDER BY id DESC LIMIT 1, [92]","duration":"19.1406ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"gform/sql_logger.go:109","msg":"SQL执行","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? LIMIT 1, [92]","duration":"19.8198ms","duration_ms":19}
