{"level":"dev.info","ts":"[2025-08-01 11:09:09.916]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"185785d9b17bf338f68877a1","method":"POST","url":"/uniapp/user/cancelAccount","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/user/cancelAccount","query":""}
{"level":"dev.info","ts":"[2025-08-01 11:10:57.562]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"185785f2c19d1cd8c041460b","method":"POST","url":"/uniapp/user/cancelAccount","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/user/cancelAccount","query":""}
{"level":"dev.info","ts":"[2025-08-01 11:11:03.038]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"185785f2c19d1cd8c041460b","method":"POST","url":"/uniapp/user/cancelAccount","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":5.4778011,"response_size":95}
{"level":"dev.info","ts":"[2025-08-01 11:14:45.703]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"18578627dfe3d79caf57d62a","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/order/get_order_bills","query":""}
{"level":"dev.info","ts":"[2025-08-01 11:14:45.729]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"18578627dfe3d79caf57d62a","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0267955,"response_size":189}
{"level":"dev.info","ts":"[2025-08-01 11:16:24.870]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1857863ef6cdad7ccd262ff3","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/order/get_order_bills","query":""}
{"level":"dev.info","ts":"[2025-08-01 11:16:29.970]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1857863ef6cdad7ccd262ff3","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":5.0996845,"response_size":189}
{"level":"dev.info","ts":"[2025-08-01 11:19:54.143]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1857866fb0686f94e3f7dfae","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/order/get_order_bills","query":""}
{"level":"dev.info","ts":"[2025-08-01 11:19:54.161]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1857866fb0686f94e3f7dfae","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0180339,"response_size":2050}
{"level":"dev.info","ts":"[2025-08-01 11:20:02.987]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"18578671bf928e44939096a1","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/order/get_order_bills","query":""}
{"level":"dev.info","ts":"[2025-08-01 11:20:03.002]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"18578671bf928e44939096a1","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0153391,"response_size":2050}
