package repayment

import (
	"errors"
	"math"
	"time"

	"fincore/model"
)

// roundToTwoDecimal 保留两位小数
func roundToTwoDecimal(value float64) float64 {
	return math.Round(value*100) / 100
}

// RepaymentPeriod 单期还款计划
type RepaymentPeriod struct {
	PeriodNumber       int     `json:"period_number"`        // 期数
	DuePrincipal       float64 `json:"due_principal"`        // 应还本金（实际是展示用的放款金额明细）
	DueInterest        float64 `json:"due_interest"`         // 应还利息
	DueGuaranteeFee    float64 `json:"due_guarantee_fee"`    // 应还担保费（进入担保账户）
	DueOtherFees       float64 `json:"due_other_fees"`       // 应还其他费用
	AssetManagementFee float64 `json:"asset_management_fee"` // 资管费（进入资管账户）
	LateFee            float64 `json:"late_fee"`             // 逾期费
	TotalDueAmount     float64 `json:"total_due_amount"`     // 当期应还总额 = 担保费 + 资管费
	DueDate            string  `json:"due_date"`             // 到期日期，格式：YYYY-MM-DD
}

// RepaymentSchedule 完整还款计划
type RepaymentSchedule struct {
	TotalPeriods            int               `json:"total_periods"`              // 总期数
	Periods                 []RepaymentPeriod `json:"periods"`                    // 各期还款计划
	TotalPrincipal          float64           `json:"total_principal"`            // 总本金
	TotalInterest           float64           `json:"total_interest"`             // 总利息
	TotalGuaranteeFee       float64           `json:"total_guarantee_fee"`        // 总担保费
	TotalOtherFees          float64           `json:"total_other_fees"`           // 总其他费用
	TotalAssetManagementFee float64           `json:"total_asset_management_fee"` // 总资管费
	TotalRepayableAmount    float64           `json:"total_repayable_amount"`     // 总应还金额
	DisbursementAmount      float64           `json:"disbursement_amount"`        // 实际放款金额
	IsPrePayment            bool              `json:"is_pre_payment"`             // 是否为前置还款
}

// CalculateRepaymentSchedule 计算还款计划
// 这是一个纯计算方法，不涉及数据库操作，可被多个场景复用
func CalculateRepaymentSchedule(productRule *model.ProductRules) (*RepaymentSchedule, error) {
	// 参数验证
	if productRule == nil {
		return nil, errors.New("产品规则不能为空")
	}
	if productRule.LoanAmount <= 0 {
		return nil, errors.New("产品规则中的贷款金额必须大于0")
	}

	// 使用当前时间作为基准日期
	startDate := time.Now()

	// 计算基础金额
	amounts := CalculateOrderAmounts(productRule.LoanAmount, productRule)

	// 获取期数
	totalPeriods := productRule.TotalPeriods
	if totalPeriods <= 0 {
		totalPeriods = 1 // 默认一期
	}

	// 计算资管费总额（本金 + 利息 + 其他费用）
	assetManagementFeeTotal := amounts.Principal + amounts.TotalInterest + amounts.TotalOtherFees

	// 按期数均摊各项费用，先计算精确值再保留两位小数
	guaranteeFeePerPeriod := amounts.TotalGuaranteeFee / float64(totalPeriods)
	assetManagementFeePerPeriod := assetManagementFeeTotal / float64(totalPeriods)
	actualDisbursementPerPeriod := amounts.Principal / float64(totalPeriods)
	interestPerPeriod := amounts.TotalInterest / float64(totalPeriods)
	otherFeesPerPeriod := amounts.TotalOtherFees / float64(totalPeriods)

	// 计算期间间隔天数：每期间隔等于借款周期天数
	// 总还款天数 = LoanPeriod * TotalPeriods
	periodDays := productRule.LoanPeriod

	// 生成各期还款计划
	periods := make([]RepaymentPeriod, 0, totalPeriods)
	for i := 1; i <= totalPeriods; i++ {
		dueDate := startDate.AddDate(0, 0, periodDays*i)

		// 计算当期应还总额 = 担保费 + 资管费，保留两位小数
		totalDueAmount := roundToTwoDecimal(guaranteeFeePerPeriod + assetManagementFeePerPeriod)

		period := RepaymentPeriod{
			PeriodNumber:       i,
			DuePrincipal:       roundToTwoDecimal(actualDisbursementPerPeriod), // 实际放款金额明细（用于展示）
			DueInterest:        roundToTwoDecimal(interestPerPeriod),           // 利息明细（用于展示）
			DueGuaranteeFee:    roundToTwoDecimal(guaranteeFeePerPeriod),       // 担保费（进入担保账户）
			DueOtherFees:       roundToTwoDecimal(otherFeesPerPeriod),          // 其他费用明细
			AssetManagementFee: roundToTwoDecimal(assetManagementFeePerPeriod), // 资管费（进入资管账户）
			LateFee:            roundToTwoDecimal(0.00),                        // 逾期费
			TotalDueAmount:     totalDueAmount,                                 // 当期应还总额
			DueDate:            dueDate.Format("2006-01-02"),
		}

		periods = append(periods, period)
	}

	// 构建完整还款计划，所有金额保留两位小数
	schedule := &RepaymentSchedule{
		TotalPeriods:            totalPeriods,
		Periods:                 periods,
		TotalPrincipal:          roundToTwoDecimal(amounts.Principal),
		TotalInterest:           roundToTwoDecimal(amounts.TotalInterest),
		TotalGuaranteeFee:       roundToTwoDecimal(amounts.TotalGuaranteeFee),
		TotalOtherFees:          roundToTwoDecimal(amounts.TotalOtherFees),
		TotalAssetManagementFee: roundToTwoDecimal(assetManagementFeeTotal),
		TotalRepayableAmount:    roundToTwoDecimal(amounts.TotalRepayableAmount),
		DisbursementAmount:      roundToTwoDecimal(amounts.DisbursementAmount),
		IsPrePayment:            amounts.IsPrePayment,
	}

	return schedule, nil
}

// OrderAmounts 订单金额结构
type OrderAmounts struct {
	Principal            float64 // 本金（申请金额）
	TotalInterest        float64 // 总利息
	TotalGuaranteeFee    float64 // 总担保费
	TotalOtherFees       float64 // 总其他费用
	TotalRepayableAmount float64 // 总还款金额
	DisbursementAmount   float64 // 实际放款金额（前置还款时会扣除费用）
	IsPrePayment         bool    // 是否为前置还款
}

// CalculateOrderAmounts 计算产品规则金额
func CalculateOrderAmounts(loanAmount float64, productRule *model.ProductRules) OrderAmounts {
	if loanAmount <= 0 || productRule == nil {
		return OrderAmounts{}
	}

	principal := roundToTwoDecimal(loanAmount)

	// 计算利息: 本金 * 年利率 * (天数/365) * 期数，使用 math 库进行精确计算
	interestRate := productRule.AnnualInterestRate / 100
	if interestRate < 0 {
		interestRate = 0
	}
	totalInterest := roundToTwoDecimal(loanAmount*interestRate*float64(productRule.LoanPeriod)/365) * float64(productRule.TotalPeriods)

	// 获取费用（确保为正数），保留两位小数
	totalGuaranteeFee := roundToTwoDecimal(math.Max(0, productRule.GuaranteeFee))
	totalOtherFees := roundToTwoDecimal(math.Max(0, productRule.OtherFees))

	// 计算总还款金额，保留两位小数
	totalRepayableAmount := roundToTwoDecimal(principal + totalInterest + totalGuaranteeFee + totalOtherFees)

	// 计算实际放款金额，保留两位小数
	disbursementAmount := roundToTwoDecimal(productRule.CalculateDisbursementAmount(loanAmount))

	return OrderAmounts{
		Principal:            principal,
		TotalInterest:        totalInterest,
		TotalGuaranteeFee:    totalGuaranteeFee,
		TotalOtherFees:       totalOtherFees,
		TotalRepayableAmount: totalRepayableAmount,
		DisbursementAmount:   disbursementAmount,
		IsPrePayment:         productRule.IsPrePayment(),
	}
}

// ConvertToRepaymentBillMaps 将还款计划转换为数据库插入用的 map 数据
func ConvertToRepaymentBillMaps(schedule *RepaymentSchedule, orderID, userID int) []map[string]interface{} {
	if schedule == nil {
		return nil
	}

	now := time.Now()
	billMaps := make([]map[string]interface{}, 0, len(schedule.Periods))

	for _, period := range schedule.Periods {
		billMap := map[string]interface{}{
			"order_id":               orderID,
			"user_id":                userID,
			"period_number":          period.PeriodNumber,
			"due_principal":          roundToTwoDecimal(period.DuePrincipal),
			"due_interest":           roundToTwoDecimal(period.DueInterest),
			"due_guarantee_fee":      roundToTwoDecimal(period.DueGuaranteeFee),
			"due_other_fees":         roundToTwoDecimal(period.DueOtherFees),
			"asset_management_entry": roundToTwoDecimal(period.AssetManagementFee),
			"late_fee":               roundToTwoDecimal(period.LateFee),
			"total_due_amount":       roundToTwoDecimal(period.TotalDueAmount),
			"paid_amount":            roundToTwoDecimal(0.00),
			"status":                 0, // 0-待还款
			"due_date":               period.DueDate,
			"deduct_retry_count":     0,
			"created_at":             now,
			"updated_at":             now,
		}
		billMaps = append(billMaps, billMap)
	}

	return billMaps
}
