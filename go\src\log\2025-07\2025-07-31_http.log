{"level":"dev.info","ts":"[2025-07-31 23:02:45.909]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"18575e360033f0a0605c7d22","method":"POST","url":"/uniapp/order/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/order/createOrder","query":""}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.368]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"18575e360033f0a0605c7d22","method":"POST","url":"/uniapp/order/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":30.459748,"response_size":864}
