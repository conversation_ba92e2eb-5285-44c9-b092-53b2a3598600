{"level":"dev.info","ts":"[2025-07-31 23:02:40.223]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.225]","caller":"scheduler/manager.go:71","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.225]","caller":"scheduler/scheduler.go:209","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.225]","caller":"scheduler/scheduler.go:218","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.225]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":1}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.226]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"*/10 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.226]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":1,"success":1,"failure":0}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"scheduler/manager.go:89","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"engine/scheduler.go:159","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"*/10 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":1,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"scheduler/manager.go:104","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":1}
{"level":"dev.info","ts":"[2025-07-31 23:02:40.227]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-07-31 23:02:50.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.warn","ts":"[2025-07-31 23:03:00.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"auto-disbursement-compensation","concurrency_mode":"singleton","running_count":1}
{"level":"dev.warn","ts":"[2025-07-31 23:03:10.000]","caller":"engine/scheduler.go:173","msg":"任务跳过执行（并发控制）","task_name":"auto-disbursement-compensation","concurrency_mode":"singleton","running_count":1}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.382]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":26.3824089,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:16.382]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-07-31 23:03:20.000]","caller":"engine/scheduler.go:186","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":1.0885462,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-07-31 23:03:21.088]","caller":"engine/scheduler.go:196","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
