package repayment

import (
	"context"
	"fmt"
	"time"

	"fincore/app/business/repayment"
	"fincore/app/scheduler/tasks"
	"fincore/utils/log"
)

// AutoWithholdTask 系统自动代扣任务
type AutoWithholdTask struct {
	*tasks.BaseTask
	logger *log.Logger
	ctx    context.Context
}

// NewAutoWithholdTask 创建系统自动代扣任务
func NewAutoWithholdTask() *AutoWithholdTask {
	baseTask := tasks.NewBaseTask(
		"auto-withhold-task",
		"系统自动代扣任务 - 自动处理待代扣账单",
		"0 10 * * *",   // 每天上午10点执行
		30*time.Minute, // 超时时间30分钟
	)

	// 设置为单例模式，避免重复执行
	baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
	// 设置重试次数和间隔
	baseTask.SetRetryCount(3).SetRetryInterval(5 * time.Minute)

	logger := log.RegisterModule("repayment_task", "还款任务")
	ctx := context.Background()
	return &AutoWithholdTask{
		BaseTask: baseTask,
		logger:   logger.WithContext(ctx),
		ctx:      ctx,
	}
}

// Execute 执行任务
func (t *AutoWithholdTask) Execute(ctx context.Context) error {

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "start_execution"),
	).Info("开始执行系统自动代扣任务")

	startTime := time.Now()

	// 执行系统自动批量代扣
	paymentService := repayment.NewPaymentService(t.ctx)
	response, err := paymentService.SystemAutoWithholdBatch()
	if err != nil {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "withhold_failed"),
			log.String("error", err.Error()),
		).Error("系统自动代扣执行失败")
		return fmt.Errorf("系统自动代扣执行失败: %v", err)
	}

	// 记录执行结果
	duration := time.Since(startTime)
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "execution_completed"),
		log.Int("total_count", response.TotalCount),
		log.Int("success_count", response.SuccessCount),
		log.Int("failed_count", response.FailureCount),
		log.String("duration", duration.String()),
	).Info("系统自动代扣执行完成")

	// 如果有失败的代扣，记录详细信息
	if response.FailureCount > 0 {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "failed_details"),
		).Warn("存在失败的代扣记录")
		for _, result := range response.Results {
			if !result.Success {
				t.logger.WithFields(
					log.String("task", t.GetName()),
					log.Int("bill_id", result.BillID),
					log.Int("user_id", result.UserID),
					log.String("error", result.ErrorMessage),
				).Error("代扣失败详情")
			}
		}
	}

	return nil
}

// OnStart 任务开始执行前的回调
func (t *AutoWithholdTask) OnStart(ctx context.Context) error {
	// task_ 开头，记录整个周期所有 sql 执行日志
	requestID := "task_" + t.GetName() + "_" + time.Now().Format("20060102150405")
	t.ctx = context.WithValue(t.ctx, log.RequestIDKey, requestID)
	t.logger = t.logger.WithRequestID(requestID)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_starting"),
	).Info("系统自动代扣任务即将开始")
	return nil
}

// OnSuccess 任务执行成功后的回调
func (t *AutoWithholdTask) OnSuccess(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_success"),
	).Info("系统自动代扣任务执行成功")
	return nil
}

// OnError 任务执行失败后的回调
func (t *AutoWithholdTask) OnError(ctx context.Context, err error) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_error"),
		log.String("error", err.Error()),
	).Error("系统自动代扣任务执行失败")

	// 可以在这里添加错误通知逻辑，比如发送邮件、短信等
	// 例如：notifyError(t.GetName(), err)
	return nil
}
